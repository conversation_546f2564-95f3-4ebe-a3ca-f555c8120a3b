import { createContext, useContext, useState, ReactNode } from 'react';
import { User, UserRole } from '../types';
import { currentUser as mockCurrentUser, users as mockUsers } from '../data/mockData';

interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  register: (name: string, email: string, password: string, role: UserRole) => Promise<boolean>;
  hasPermission: (requiredRole: UserRole) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(mockCurrentUser);
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Set to true for demo purposes

  const login = async (email: string, password: string): Promise<boolean> => {
    // In a real app, this would make an API call
    // For demo purposes, we'll simulate a successful login
    try {
      const user = mockUsers.find(u => u.email === email);
      if (user) {
        setCurrentUser(user);
        setIsAuthenticated(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const register = async (
    name: string, 
    email: string, 
    password: string, 
    role: UserRole
  ): Promise<boolean> => {
    // In a real app, this would make an API call
    // For demo purposes, we'll simulate a successful registration
    try {
      // Check if user already exists
      if (mockUsers.some(u => u.email === email)) {
        return false;
      }
      
      // In a real app, this would add the user to the database
      // For demo, we just set as current user
      const newUser: User = {
        id: `user-${Date.now()}`,
        name,
        email,
        role,
      };
      
      setCurrentUser(newUser);
      setIsAuthenticated(true);
      return true;
    } catch (error) {
      console.error('Registration error:', error);
      return false;
    }
  };

  const hasPermission = (requiredRole: UserRole): boolean => {
    if (!currentUser) return false;
    
    // Admin has all permissions
    if (currentUser.role === UserRole.ADMIN) return true;
    
    // Simple role check - in a real app, this would be more complex
    return currentUser.role === requiredRole;
  };

  return (
    <AuthContext.Provider 
      value={{ 
        currentUser, 
        isAuthenticated, 
        login, 
        logout, 
        register,
        hasPermission
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};