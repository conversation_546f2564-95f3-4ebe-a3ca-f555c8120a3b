import React from 'react';
import { Story } from '../../types';
import Badge from '../ui/Badge';
import Avatar from '../ui/Avatar';
import { users } from '../../data/mockData';

interface StoryCardProps {
  story: Story;
  onClick?: (storyId: string) => void;
}

const StoryCard: React.FC<StoryCardProps> = ({ story, onClick }) => {
  const assignee = story.assigneeId 
    ? users.find(u => u.id === story.assigneeId) 
    : null;
    
  return (
    <div 
      className="p-3 bg-white border border-gray-200 rounded-md shadow-sm hover:shadow transition-shadow cursor-pointer"
      onClick={() => onClick && onClick(story.id)}
    >
      <div className="flex justify-between items-start gap-2">
        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 flex-1">{story.title}</h4>
        {/* <Badge variant="priority" value={story.priority} className="flex-shrink-0" /> */}
      </div>
      
      <p className="mt-1 text-xs text-gray-500 line-clamp-2">{story.description}</p>
      
      <div className="mt-3 flex items-center justify-between">
        <div className="flex flex-wrap gap-1 max-w-[70%]">
          {/* {story.tags.map(tag => (
            <span key={tag} className="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs truncate">
              {tag}
            </span>
          ))} */}
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          {/* <span className="text-xs font-semibold px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full">
            {story.points} pts
          </span> */}
          
          {assignee && (
            <Avatar
              src={assignee.avatar}
              name={assignee.name}
              size="sm"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default StoryCard;