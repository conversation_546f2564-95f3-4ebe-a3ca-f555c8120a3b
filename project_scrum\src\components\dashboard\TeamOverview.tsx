import React from 'react';
import { User, Story } from '../../types';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import { CheckCircle2, Circle, Clock } from 'lucide-react';

interface TeamOverviewProps {
  teamMembers: User[];
  stories: Story[];
}

const TeamOverview: React.FC<TeamOverviewProps> = ({ teamMembers, stories }) => {
  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900">Team Overview</h3>
      </div>
      
      <div className="divide-y divide-gray-200">
        {teamMembers.map(member => {
          const assignedStories = stories.filter(s => s.assigneeId === member.id);
          const DoneStories = assignedStories.filter(s => s.status === 'done');
          const CheckStories = assignedStories.filter(s => s.status === 'check');
          const WorkingStories = assignedStories.filter(s => s.status === 'working');
          const BacklogStories = assignedStories.filter(s => s.status === 'backlog');
          
          return (
            <div key={member.id} className="p-4 flex items-start hover:bg-gray-50">
              <Avatar 
                src={member.avatar} 
                name={member.name} 
                size="md"
              />
              
              <div className="ml-3 flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">{member.name}</h4>
                  <Badge 
                    variant="default" 
                    value={member.role} 
                    className="capitalize"
                  />
                </div>
                
                <div className="mt-2 text-xs text-gray-500">
                  {assignedStories.length === 0 ? (
                    <p>No tasks assigned</p>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CheckCircle2 className="h-3.5 w-3.5 text-green-500 mr-1" />
                          <span>Done</span>
                        </div>
                        <span>{DoneStories.length}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Clock className="h-3.5 w-3.5 text-purple-500 mr-1" />
                          <span>Check</span>
                        </div>
                        <span>{CheckStories.length}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Circle className="h-3.5 w-3.5 text-yellow-400 mr-1" />
                          <span>Working</span>
                        </div>
                        <span>{WorkingStories.length}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Circle className="h-3.5 w-3.5 text-red-400 mr-1" />
                          <span>Backlog</span>
                        </div>
                        <span>{BacklogStories.length}</span>
                      </div>
                      
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TeamOverview;