
from django.db import models

class Task(models.Model):
    s_no = models.IntegerField(primary_key=True)
    project_zone_no_zone = models.CharField(max_length=100)
    active_list = models.CharField(max_length=100, blank=True, null=True)
    job_description = models.TextField(blank=True, null=True)
    expected_hours = models.IntegerField(blank=True, null=True)
    consumed_hours = models.IntegerField(blank=True, null=True)
    zone = models.CharField(max_length=10, blank=True, null=True)
    responsible_1 = models.CharField(max_length=100, blank=True, null=True)
    responsible_2 = models.CharField(max_length=100, blank=True, null=True)
    job_status = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False  
        db_table = 'job_tracking'  

