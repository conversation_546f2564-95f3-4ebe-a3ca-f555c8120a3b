import { useState, useEffect } from 'react';
import { taskAPI, transformBackendTask, BackendTask } from '../services/api';
import { Task } from '../types';

interface UseTaskDataReturn {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getTasksByZone: (zone: string) => Task[];
  getTasksByStatus: (status: string) => Task[];
  createTask: (taskData: Omit<Task, 'id'>) => Promise<void>;
  updateTask: (id: string, taskData: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}

export const useTaskData = (): UseTaskDataReturn => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(null);
      const backendTasks = await taskAPI.getAllTasks();
      const transformedTasks = backendTasks.map(transformBackendTask);
      setTasks(transformedTasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks');
      console.error('Error fetching tasks:', err);
    } finally {
      setLoading(false);
    }
  };

  const refetch = async () => {
    await fetchTasks();
  };

  const getTasksByZone = (zone: string): Task[] => {
    return tasks.filter(task => task.zone === zone);
  };

  const getTasksByStatus = (status: string): Task[] => {
    return tasks.filter(task => task.status === status);
  };

  const createTask = async (taskData: Omit<Task, 'id'>) => {
    try {
      setError(null);
      const backendTaskData = {
        project_zone_no_zone: taskData.projectId,
        active_list: taskData.activeList || null,
        job_description: taskData.description || null,
        expected_hours: taskData.expectedHours || null,
        consumed_hours: taskData.consumedHours || null,
        zone: taskData.zone || null,
        responsible_1: taskData.assignedTo || null,
        responsible_2: taskData.assignedTo2 || null,
        job_status: taskData.status || null,
      };
      
      const newBackendTask = await taskAPI.createTask(backendTaskData);
      const newTask = transformBackendTask(newBackendTask);
      setTasks(prevTasks => [...prevTasks, newTask]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create task');
      throw err;
    }
  };

  const updateTask = async (id: string, taskData: Partial<Task>) => {
    try {
      setError(null);
      const backendTaskData: Partial<BackendTask> = {};
      
      if (taskData.projectId !== undefined) backendTaskData.project_zone_no_zone = taskData.projectId;
      if (taskData.activeList !== undefined) backendTaskData.active_list = taskData.activeList;
      if (taskData.description !== undefined) backendTaskData.job_description = taskData.description;
      if (taskData.expectedHours !== undefined) backendTaskData.expected_hours = taskData.expectedHours;
      if (taskData.consumedHours !== undefined) backendTaskData.consumed_hours = taskData.consumedHours;
      if (taskData.zone !== undefined) backendTaskData.zone = taskData.zone;
      if (taskData.assignedTo !== undefined) backendTaskData.responsible_1 = taskData.assignedTo;
      if (taskData.assignedTo2 !== undefined) backendTaskData.responsible_2 = taskData.assignedTo2;
      if (taskData.status !== undefined) backendTaskData.job_status = taskData.status;

      const updatedBackendTask = await taskAPI.updateTask(parseInt(id), backendTaskData);
      const updatedTask = transformBackendTask(updatedBackendTask);
      
      setTasks(prevTasks => 
        prevTasks.map(task => task.id === id ? updatedTask : task)
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update task');
      throw err;
    }
  };

  const deleteTask = async (id: string) => {
    try {
      setError(null);
      await taskAPI.deleteTask(parseInt(id));
      setTasks(prevTasks => prevTasks.filter(task => task.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete task');
      throw err;
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  return {
    tasks,
    loading,
    error,
    refetch,
    getTasksByZone,
    getTasksByStatus,
    createTask,
    updateTask,
    deleteTask,
  };
};

// Hook for getting aggregated task statistics
export const useTaskStats = () => {
  const { tasks } = useTaskData();

  const getStatsByZone = () => {
    const zoneStats: Record<string, { total: number; byStatus: Record<string, number> }> = {};
    
    tasks.forEach(task => {
      const zone = task.zone || 'Unknown';
      const status = task.status || 'Unknown';
      
      if (!zoneStats[zone]) {
        zoneStats[zone] = { total: 0, byStatus: {} };
      }
      
      zoneStats[zone].total++;
      zoneStats[zone].byStatus[status] = (zoneStats[zone].byStatus[status] || 0) + 1;
    });
    
    return zoneStats;
  };

  const getStatsByStatus = () => {
    const statusStats: Record<string, number> = {};
    
    tasks.forEach(task => {
      const status = task.status || 'Unknown';
      statusStats[status] = (statusStats[status] || 0) + 1;
    });
    
    return statusStats;
  };

  const getTotalHours = () => {
    return tasks.reduce((total, task) => {
      return {
        expected: total.expected + (task.expectedHours || 0),
        consumed: total.consumed + (task.consumedHours || 0),
      };
    }, { expected: 0, consumed: 0 });
  };

  return {
    tasks,
    getStatsByZone,
    getStatsByStatus,
    getTotalHours,
  };
};
