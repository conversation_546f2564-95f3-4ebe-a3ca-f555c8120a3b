{% comment %} <!DOCTYPE html>
<html>
<head>
    <title>Job Tracking Dashboard</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Job Tracking Dashboard</h1>
    <table>
        <tr>
            <th>S.No</th>
            <th>Project Zone No/Zone</th>
            <th>Active List</th>
            <th>Job Description</th>
            <th>Expected Hours</th>
            <th>Consumed Hours</th>
            <th>Zone</th>
            <th>Responsible 1</th>
            <th>Responsible 2</th>
            <th>Job Status</th>
        </tr>
        {% for job in jobs %}
        <tr>
            <td>{{ job.s_no }}</td>
            <td>{{ job.project_zone_no_zone }}</td>
            <td>{{ job.active_list }}</td>
            <td>{{ job.job_description }}</td>
            <td>{{ job.expected_hours }}</td>
            <td>{{ job.consumed_hours }}</td>
            <td>{{ job.zone }}</td>
            <td>{{ job.responsible_1 }}</td>
            <td>{{ job.responsible_2 }}</td>
            <td>{{ job.job_status }}</td>
        </tr>
        {% endfor %}
    </table>
</body>
</html>
 {% endcomment %}


<!DOCTYPE html>
<html>
<head>
    <title>Dashboard</title>
</head>
<body>
    <h1>Task Dashboard</h1>
    <table border="1">
        <thead>
            <tr>
                <th>S.No</th>
                <th>Project_zone_no_zone</th>
                <th>Active List</th>
                <th>Job Description</th>
                <th>Expected Hours</th>
                <th>Consumed Hours</th>
                <th>Zone</th>
                <th>Responsible 1</th>
                <th>Responsible 2</th>
                <th>Job Status</th>
            </tr>
        </thead>
        <tbody>
            {% for task in tasks %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ task.project_zone_no_zone }}</td>
                <td>{{ task.active_list }}</td>
                <td>{{ task.job_description }}</td>
                <td>{{ task.expected_hours }}</td>
                <td>{{ task.consumed_hours }}</td>
                <td>{{ task.zone }}</td>
                <td>{{ task.responsible_1 }}</td>
                <td>{{ task.responsible_2 }}</td>
                <td>{{ task.job_status }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="11">No tasks found.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
