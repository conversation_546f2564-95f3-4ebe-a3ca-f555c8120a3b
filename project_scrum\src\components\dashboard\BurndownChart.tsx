import React from 'react';
import { Story, Sprint, Status } from '../../types';

interface BurndownChartProps {
  sprint: Sprint;
  stories: Story[];
}

const BurndownChart: React.FC<BurndownChartProps> = ({ sprint, stories }) => {
  // For a real implementation, we'd use a proper charting library
  // This is a simplified visual representation
  
  const totalPoints = stories.reduce((acc, story) => acc + story.points, 0);
  const completedPoints = stories
    .filter(s => s.status === Status.DONE)
    .reduce((acc, story) => acc + story.points, 0);
  const remainingPoints = totalPoints - completedPoints;
  
  // Sprint duration in days
  const startDate = new Date(sprint.startDate);
  const endDate = new Date(sprint.endDate);
  const sprintDuration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Days elapsed
  const now = new Date();
  const daysElapsed = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const daysElapsedClamped = Math.max(0, Math.min(daysElapsed, sprintDuration));
  
  // Expected burn rate (ideal burndown)
  const dailyBurnRate = totalPoints / sprintDuration;
  const expectedRemaining = Math.max(0, totalPoints - (dailyBurnRate * daysElapsedClamped));
  
  // Create points for the chart
  const days = Array.from({ length: sprintDuration + 1 }, (_, i) => i);
  
  // Chart dimensions
  const chartWidth = 300;
  const chartHeight = 150;
  const paddingX = 40;
  const paddingY = 20;
  const graphWidth = chartWidth - (paddingX * 2);
  const graphHeight = chartHeight - (paddingY * 2);
  
  // Scale factors
  const scaleX = graphWidth / sprintDuration;
  const scaleY = graphHeight / totalPoints;

  // Generate ideal burndown line points
  const idealLine = days.map(day => {
    const x = paddingX + (day * scaleX);
    const y = paddingY + ((totalPoints - (day * dailyBurnRate)) * scaleY);
    return `${x},${y}`;
  }).join(' ');
  
  // Generate actual burndown line (we're simulating here)
  // In a real app, we'd use historical data
  
  // For demo purposes, let's create a somewhat realistic actual line
  const actualPoints = [totalPoints];
  let remainingPointsSimulation = totalPoints;
  
  for (let i = 1; i <= daysElapsedClamped; i++) {
    // This logic would be replaced with actual historical data
    if (i < daysElapsedClamped) {
      // Random daily progress with some variation
      const randomProgress = Math.floor(Math.random() * dailyBurnRate * 2);
      remainingPointsSimulation = Math.max(0, remainingPointsSimulation - randomProgress);
      actualPoints.push(remainingPointsSimulation);
    } else {
      // The last point is the actual remaining points
      actualPoints.push(remainingPoints);
    }
  }
  
  const actualLine = actualPoints.map((points, index) => {
    const x = paddingX + (index * scaleX);
    const y = paddingY + (points * scaleY);
    return `${x},${y}`;
  }).join(' ');

  return (
    <div className="p-6 bg-white shadow-sm rounded-lg">
      <h3 className="text-lg font-medium text-gray-900">Burndown Chart</h3>
      
      <div className="mt-4">
        <svg width={chartWidth} height={chartHeight} viewBox={`0 0 ${chartWidth} ${chartHeight}`} className="mx-auto">
          {/* X and Y axes */}
          <line 
            x1={paddingX} 
            y1={chartHeight - paddingY} 
            x2={chartWidth - paddingX} 
            y2={chartHeight - paddingY} 
            stroke="#CBD5E1" 
            strokeWidth="1"
          />
          <line 
            x1={paddingX} 
            y1={paddingY} 
            x2={paddingX} 
            y2={chartHeight - paddingY} 
            stroke="#CBD5E1" 
            strokeWidth="1"
          />
          
          {/* Horizontal grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => (
            <React.Fragment key={i}>
              <line 
                x1={paddingX} 
                y1={paddingY + (graphHeight * ratio)} 
                x2={chartWidth - paddingX} 
                y2={paddingY + (graphHeight * ratio)} 
                stroke="#EDF2F7" 
                strokeWidth="1"
              />
              <text 
                x={paddingX - 5} 
                y={paddingY + (graphHeight * ratio)} 
                fontSize="8" 
                textAnchor="end" 
                dominantBaseline="middle"
                fill="#64748B"
              >
                {Math.round(totalPoints * (1 - ratio))}
              </text>
            </React.Fragment>
          ))}
          
          {/* Labels for start and end */}
          <text 
            x={paddingX} 
            y={chartHeight - paddingY + 12} 
            fontSize="8" 
            textAnchor="middle"
            fill="#64748B"
          >
            Start
          </text>
          <text 
            x={chartWidth - paddingX} 
            y={chartHeight - paddingY + 12} 
            fontSize="8" 
            textAnchor="middle"
            fill="#64748B"
          >
            End
          </text>
          
          {/* Day markers */}
          {days.filter(day => day % Math.ceil(sprintDuration / 5) === 0).map(day => (
            <React.Fragment key={day}>
              <line 
                x1={paddingX + (day * scaleX)} 
                y1={chartHeight - paddingY} 
                x2={paddingX + (day * scaleX)} 
                y2={chartHeight - paddingY + 4} 
                stroke="#64748B" 
                strokeWidth="1"
              />
              <text 
                x={paddingX + (day * scaleX)} 
                y={chartHeight - paddingY + 12} 
                fontSize="8" 
                textAnchor="middle"
                fill="#64748B"
              >
                Day {day}
              </text>
            </React.Fragment>
          ))}
          
          {/* Ideal burndown line */}
          <polyline 
            points={idealLine} 
            fill="none" 
            stroke="#94A3B8" 
            strokeWidth="1" 
            strokeDasharray="2,2"
          />
          
          {/* Actual burndown line */}
          <polyline 
            points={actualLine} 
            fill="none" 
            stroke="#2563EB" 
            strokeWidth="2"
          />
          
          {/* Current day marker */}
          {daysElapsedClamped > 0 && daysElapsedClamped <= sprintDuration && (
            <circle 
              cx={paddingX + (daysElapsedClamped * scaleX)} 
              cy={paddingY + (remainingPoints * scaleY)} 
              r="3" 
              fill="#2563EB"
            />
          )}
        </svg>
      </div>
      
      <div className="mt-4 flex items-center justify-center space-x-6 text-xs text-gray-600">
        <div className="flex items-center">
          <div className="w-3 h-1 bg-blue-600 mr-1"></div>
          <span>Actual</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-1 bg-gray-400 mr-1 border-dashed border-gray-400"></div>
          <span>Ideal</span>
        </div>
      </div>
      
      <div className="mt-4 grid grid-cols-2 gap-4">
        <div className="text-center p-2 bg-blue-50 rounded">
          <h4 className="text-xs text-gray-700">Completed</h4>
          <p className="font-semibold text-blue-700">{completedPoints} points</p>
        </div>
        <div className="text-center p-2 bg-orange-50 rounded">
          <h4 className="text-xs text-gray-700">Remaining</h4>
          <p className="font-semibold text-orange-700">{remainingPoints} points</p>
        </div>
      </div>
    </div>
  );
};

export default BurndownChart;