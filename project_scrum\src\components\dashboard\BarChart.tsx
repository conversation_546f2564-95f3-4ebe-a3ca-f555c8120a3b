import React from 'react';
import { Story, Status } from '../../types';

interface BarChartProps {
  stories: Story[];
}

const BarChart: React.FC<BarChartProps> = ({ stories }) => {
  const total = stories.length;

  // Count stories per status
  const backlogCount = stories.filter(s => s.status === Status.BACKLOG).length;
  const workingCount = stories.filter(s => s.status === Status.WORKING).length;
  const checkCount = stories.filter(s => s.status === Status.CHECK).length;
  const doneCount = stories.filter(s => s.status === Status.DONE).length;

  // Convert count to percentage
  const calculatePercentage = (count: number) => {
    return total === 0 ? 0 : Math.round((count / total) * 100);
  };

  const data = [
    {
      label: 'Backlog',
      count: backlogCount,
      percentage: calculatePercentage(backlogCount),
      color: 'bg-red-400',
    },
    {
      label: 'Working',
      count: workingCount,
      percentage: calculatePercentage(workingCount),
      color: 'bg-yellow-400',
    },
    {
      label: 'Check',
      count: checkCount,
      percentage: calculatePercentage(checkCount),
      color: 'bg-purple-400',
    },
    {
      label: 'Done',
      count: doneCount,
      percentage: calculatePercentage(doneCount),
      color: 'bg-green-400',
    },
  ];

  return (
    <div className="p-6 bg-white shadow-sm rounded-lg">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Project Status</h3>
      <div className="space-y-4">
        {data.map(({ label, count, percentage, color }) => (
          <div key={label}>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">{label}</span>
              <span className="text-sm text-gray-500">{percentage}% ({count} projects)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div
                className={`${color} h-4 rounded-full transition-all duration-300`}
                style={{ width: `${percentage}%` }}
              />
            </div>
          </div>
        ))}
      </div>
      <div className="mt-6 text-center text-sm text-gray-500">
        Total Projects: <span className="font-semibold">{total}</span>
      </div>
    </div>
  );
};

export default BarChart;
