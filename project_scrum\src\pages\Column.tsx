import { useState, ChangeEvent } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { Task } from '../types';
import Card from './Card';

type ColumnProps = {
  columnId: string;
  tasks: Task[];
  updateTask: (taskId: string, updatedTask: Partial<Task>) => void;
  deleteTask: (taskId: string, columnId: string) => void;
};

const columnNames: Record<string, string> = {
  todo: 'Backlog',
  inProgress: 'Working',
  check: 'Check',
  done: 'Done',
};

function Column({ columnId, tasks, updateTask, deleteTask }: ColumnProps) {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [newTitle, setNewTitle] = useState(columnNames[columnId]);

  const handleTitleClick = () => {
    setIsEditingTitle(true);
  };

  const handleTitleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setNewTitle(e.target.value);
  };

  const handleTitleSave = () => {
    columnNames[columnId] = newTitle;
    setIsEditingTitle(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-5 w-80 flex flex-col">
      {isEditingTitle ? (
        <div>
          <input
            type="text"
            value={newTitle}
            onChange={handleTitleChange}
            className="text-xl font-semibold mb-4 p-2 border rounded"
          />
          <button onClick={handleTitleSave} className="text-blue-500">
            Save
          </button>
        </div>
      ) : (
        <h2 onClick={handleTitleClick} className="text-xl font-semibold mb-4 cursor-pointer">
          {newTitle}
        </h2>
      )}
      <Droppable droppableId={columnId}>
        {(provided, snapshot) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className={`flex flex-col space-y-4 p-2 rounded min-h-[100px] transition-colors ${
              snapshot.isDraggingOver ? 'bg-blue-100' : 'bg-gray-100'
            }`}
          >
            {tasks.map((task, index) => (
              <Card
                key={task.id}
                task={task}
                index={index}
                columnId={columnId}
                updateTask={updateTask}
                deleteTask={deleteTask}
              />
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
}

export default Column;
