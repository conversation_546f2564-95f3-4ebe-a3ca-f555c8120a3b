import { useState } from "react";
import { Draggable } from "@hello-pangea/dnd";
import Modal from "./Modal";
import { initialMembers } from "../data/member"; // Make sure this path is correct

type Task = {
  id: string;
  projectId: string;
  assignedTo: string;
  assignedTo2?: string;
  status?: string;
  description: string;
  expectedHours: number;
  consumedHours: number;
  jobTitle?: string;
  team?: string;
  jobNumber?: number;
};

type CardProps = {
  task: Task;
  index: number;
  columnId: string;
  updateTask: (taskId: string, updatedTask: Partial<Task>) => void;
  deleteTask: (taskId: string, columnId: string) => void;
  moveTaskToColumn: (taskId: string, newTeam: string) => void;
};

const companyMap: { [key: string]: string } = {
  P004976: "JSW",
  P005377: "JSW",
  P005766: "JSPL",
  P005960: "JSPL",
  P006006: "AMNS",
  P006049: "AMNS",
  P006198: "JSW",
  P006205: "JSW",
  P006273: "JSW",
  P006384: "JSPL",
  P006416: "JSPL",
};

const companyColors: { [key: string]: string } = {
  JSW: "bg-pink-200",
  JSPL: "bg-yellow-200",
  AMNS: "bg-blue-300",
};

function Card({ task, index, columnId, updateTask, deleteTask, moveTaskToColumn }: CardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [newProjectId, setNewProjectId] = useState(task.projectId);
  const [newAssignedTo, setNewAssignedTo] = useState(task.assignedTo);
  const [newAssignedTo2, setNewAssignedTo2] = useState(task.assignedTo2 || "");
  const [newDescription, setNewDescription] = useState(task.description);
  const [newJobTitle, setNewJobTitle] = useState(task.jobTitle || "");
  const [newTeam, setNewTeam] = useState(task.team || "");
  const [newJobNumber, setNewJobNumber] = useState<number>(task.jobNumber || 0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleEditClick = () => setIsEditing(true);

  const handleSaveClick = () => {
    const teamChanged = task.team !== newTeam;

    updateTask(task.id, {
      projectId: newProjectId,
      assignedTo: newAssignedTo,
      assignedTo2: newAssignedTo2,
      description: newDescription,
      jobTitle: newJobTitle,
      team: newTeam,
      jobNumber: newJobNumber,
    });

    if (teamChanged) {
      moveTaskToColumn(task.id, newTeam);
    }

    setIsEditing(false);
  };

  const handleDeleteClick = () => deleteTask(task.id, columnId);
  const handleShowMoreClick = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);

  const company = companyMap[task.projectId];
  const cardColor = companyColors[company] || "bg-white";

  const teamMembers = initialMembers.filter((m) => m.team === newTeam);

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          ref={provided.innerRef}
          className={`${cardColor} p-4 rounded-md shadow-md text-sm border border-black font-sans transition cursor-move ${
            snapshot.isDragging ? "opacity-70" : ""
          }`}
        >
          {isEditing ? (
            <div className="space-y-2">
              <input
                type="text"
                placeholder="Project ID"
                className="border p-2 rounded w-full"
                value={newProjectId}
                onChange={(e) => setNewProjectId(e.target.value)}
              />

              <div className="flex gap-2">
                <select
                  className="border p-2 rounded w-1/2"
                  value={newAssignedTo}
                  onChange={(e) => setNewAssignedTo(e.target.value)}
                  disabled={!newTeam}
                >
                  <option value="">Responsible-1</option>
                  {teamMembers.map((m) => (
                    <option key={m.name} value={m.name}>
                      {m.name}
                    </option>
                  ))}
                </select>

                <select
                  className="border p-2 rounded w-1/2"
                  value={newAssignedTo2}
                  onChange={(e) => setNewAssignedTo2(e.target.value)}
                  disabled={!newTeam}
                >
                  <option value="">Responsible-2</option>
                  {initialMembers.map((m) => (
                    <option key={m.name} value={m.name}>
                      {m.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex flex-wrap gap-2">
                <select
                  className="flex-1 border p-2 rounded text-sm appearance-none"
                  value={newJobTitle}
                  onChange={(e) => setNewJobTitle(e.target.value)}
                  title={newJobTitle}
                >
                  <option value="">Select Job Title</option>
                  <option value="As Built Drawing Preparation">As Built Drawing Preparation</option>
                  <option value="B/O Items-Specs Prep.">B/O Items-Specs Prep.</option>
                  <option value="B/O Items offer Scrutiny/Approval">B/O Items offer Scrutiny/Approval</option>
                  <option value="Basic Engineering">Basic Engineering</option>
                  <option value="BOM Checking">BOM Checking</option>
                  <option value="E Plan Drawing Preparation">E Plan Drawing Preparation</option>
                  <option value="BOM Preparation">BOM Preparation</option>
                  <option value="Business Development Activities">Business Development Activities</option>
                </select>

                <select
                  className="w-[30%] border p-2 rounded"
                  value={newTeam}
                  onChange={(e) => {
                    setNewTeam(e.target.value);
                    setNewAssignedTo("");
                    setNewAssignedTo2("");
                  }}
                >
                  <option value="">Zone</option>
                  <option value="SZ">SZ</option>
                  <option value="IZ">IZ</option>
                  <option value="L1">L1</option>
                  <option value="HW">HW</option>
                </select>

                <input
                  type="number"
                  placeholder="SNo."
                  className="w-[30%] border p-2 rounded"
                  value={newJobNumber}
                  onChange={(e) => setNewJobNumber(Number(e.target.value))}
                />
              </div>

              <input
                type="text"
                placeholder="Description"
                className="border p-2 rounded w-full"
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
              />

              <div className="flex gap-2">
                <button onClick={handleSaveClick} className="bg-green-500 text-white px-4 py-1 rounded">
                  Save
                </button>
                <button onClick={() => setIsEditing(false)} className="text-gray-600 underline">
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center gap-2 font-bold border-b border-black pb-1 mb-2">
                <img src="/dci_logo.jpg" alt="Danieli Corus Logo" className="w-6 h-6 object-contain" />
                PROJECT / SPRINT
              </div>

              <div className="mb-2">
                <div className="flex justify-between mb-1 gap-2 font-semibold">
                  <span className="text-sm truncate">Project: {task.projectId || "—"}</span>
                  <span>Zone: {task.team || "—"}</span>
                  <span>SNo.: {task.jobNumber ?? "—"}</span>
                </div>
                <div>
                  <div className="font-semibold mb-1">Activity</div>
                  <div className="border border-black p-1 text-xs text-center truncate" title={task.jobTitle}>
                    {task.jobTitle || "—"}
                  </div>
                </div>
              </div>

              <div className="font-semibold mb-2">Job Description</div>
              <div className="border border-black p-2 mb-2 max-h-40 overflow-y-auto whitespace-pre-wrap break-words">
                {task.description}
              </div>

              <div className="flex gap-2">
                <div className="flex-1">
                  <div className="font-semibold">Responsible-1</div>
                  <div className="border border-black p-1">{task.assignedTo || "—"}</div>
                </div>
                <div className="flex-1">
                  <div className="font-semibold">Responsible-2</div>
                  <div className="border border-black p-1">{task.assignedTo2 || "—"}</div>
                </div>
              </div>

              <div className="mt-1">
                <div className="font-semibold">Status</div>
                <div className="border border-black p-1">{task.status || "—"}</div>
              </div>

              <div className="mt-3 flex gap-2">
                <button onClick={handleEditClick} className="text-blue-600 underline">Edit</button>
                <button onClick={handleDeleteClick} className="text-red-800 underline">Delete</button>
                <button onClick={handleShowMoreClick} className="text-green-600 underline">Show More</button>
              </div>
            </>
          )}

          {isModalOpen && (
            <Modal
              taskId={task.id}
              stages={["Backlog", "Working", "Re Working", "Check", "Re Check", "Done", "Re Done"]}
              onClose={handleCloseModal}
            />
          )}
        </div>
      )}
    </Draggable>
  );
}

export default Card;
