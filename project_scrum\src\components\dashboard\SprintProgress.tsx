import React from 'react';
import { Sprint, Story, Status } from '../../types';

interface SprintProgressProps {
  sprint: Sprint;
  stories: Story[];
}

const SprintProgress: React.FC<SprintProgressProps> = ({ sprint, stories }) => {
  if (!stories.length) {
    return (
      <div className="p-6 bg-white shadow-sm rounded-lg">
        <h3 className="text-lg font-medium text-gray-900">Sprint Progress</h3>
        <p className="mt-2 text-gray-500">No stories in this sprint yet.</p>
      </div>
    );
  }

  // Calculate counts
  const totalStories = stories.length;
  const doneStories = stories.filter(s => s.status === Status.DONE).length;
  const inProgressStories = stories.filter(s => s.status === Status.IN_PROGRESS).length;
  const todoStories = stories.filter(s => s.status === Status.TODO).length;
  
  // Calculate percentages
  const donePercentage = Math.round((doneStories / totalStories) * 100);
  const inProgressPercentage = Math.round((inProgressStories / totalStories) * 100);
  const todoPercentage = Math.round((todoStories / totalStories) * 100);
  
  // Calculate total story points
  const totalPoints = stories.reduce((acc, story) => acc + story.points, 0);
  const completedPoints = stories
    .filter(s => s.status === Status.DONE)
    .reduce((acc, story) => acc + story.points, 0);
  const completedPointsPercentage = totalPoints > 0 
    ? Math.round((completedPoints / totalPoints) * 100)
    : 0;

  return (
    <div className="p-6 bg-white shadow-sm rounded-lg">
      <h3 className="text-lg font-medium text-gray-900">Sprint Progress</h3>
      
      <div className="mt-4">
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">Stories</span>
          <span className="text-sm font-medium text-gray-700">
            {doneStories} of {totalStories} completed
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div 
            className="bg-green-600 h-2.5 rounded-full" 
            style={{ width: `${donePercentage}%` }}
          ></div>
        </div>
        
        <div className="flex mt-2 text-xs text-gray-600 justify-between">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
            <span>Done ({donePercentage}%)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-600 rounded-full mr-1"></div>
            <span>In Progress ({inProgressPercentage}%)</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-400 rounded-full mr-1"></div>
            <span>To Do ({todoPercentage}%)</span>
          </div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">Story Points</span>
          <span className="text-sm font-medium text-gray-700">
            {completedPoints} of {totalPoints} points
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div 
            className="bg-blue-600 h-2.5 rounded-full" 
            style={{ width: `${completedPointsPercentage}%` }}
          ></div>
        </div>
      </div>
      
      <div className="mt-6">
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">Time Remaining</span>
          <span className="text-sm font-medium text-gray-700">
            {getTimeRemaining(sprint.endDate)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div 
            className="bg-purple-600 h-2.5 rounded-full" 
            style={{ width: `${getTimePercentage(sprint.startDate, sprint.endDate)}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

// Helper function to calculate time remaining
function getTimeRemaining(endDate: Date): string {
  const now = new Date();
  const end = new Date(endDate);
  
  if (now > end) return 'Sprint ended';
  
  const diffTime = Math.abs(end.getTime() - now.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays === 1 ? '1 day remaining' : `${diffDays} days remaining`;
}

// Helper function to calculate percentage of time elapsed
function getTimePercentage(startDate: Date, endDate: Date): number {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const totalDuration = end.getTime() - start.getTime();
  const elapsedDuration = now.getTime() - start.getTime();
  
  if (now < start) return 0;
  if (now > end) return 100;
  
  return 100 - Math.round((elapsedDuration / totalDuration) * 100);
}

export default SprintProgress;