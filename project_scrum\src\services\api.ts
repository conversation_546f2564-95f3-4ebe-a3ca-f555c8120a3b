import axios from 'axios';

// Backend API base URL
const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Task API interface matching Django backend
export interface BackendTask {
  s_no: number;
  project_zone_no_zone: string;
  active_list: string | null;
  job_description: string | null;
  expected_hours: number | null;
  consumed_hours: number | null;
  zone: string | null;
  responsible_1: string | null;
  responsible_2: string | null;
  job_status: string | null;
}

// API service functions
export const taskAPI = {
  // Get all tasks
  getAllTasks: async (): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get('/tasks/');
      return response.data;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  },

  // Get task by ID
  getTaskById: async (id: number): Promise<BackendTask> => {
    try {
      const response = await apiClient.get(`/tasks/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task ${id}:`, error);
      throw error;
    }
  },

  // Create new task
  createTask: async (taskData: Omit<BackendTask, 's_no'>): Promise<BackendTask> => {
    try {
      const response = await apiClient.post('/tasks/', taskData);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  // Update task
  updateTask: async (id: number, taskData: Partial<BackendTask>): Promise<BackendTask> => {
    try {
      const response = await apiClient.patch(`/tasks/${id}/`, taskData);
      return response.data;
    } catch (error) {
      console.error(`Error updating task ${id}:`, error);
      throw error;
    }
  },

  // Delete task
  deleteTask: async (id: number): Promise<void> => {
    try {
      await apiClient.delete(`/tasks/${id}/`);
    } catch (error) {
      console.error(`Error deleting task ${id}:`, error);
      throw error;
    }
  },

  // Get tasks by zone
  getTasksByZone: async (zone: string): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get(`/tasks/?zone=${zone}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tasks for zone ${zone}:`, error);
      throw error;
    }
  },

  // Get tasks by status
  getTasksByStatus: async (status: string): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get(`/tasks/?job_status=${status}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tasks with status ${status}:`, error);
      throw error;
    }
  },
};

// Helper function to transform backend task to frontend format
export const transformBackendTask = (backendTask: BackendTask) => {
  return {
    id: backendTask.s_no.toString(),
    projectId: backendTask.project_zone_no_zone,
    assignedTo: backendTask.responsible_1 || '',
    assignedTo2: backendTask.responsible_2 || '',
    description: backendTask.job_description || '',
    expectedHours: backendTask.expected_hours || 0,
    consumedHours: backendTask.consumed_hours || 0,
    status: backendTask.job_status || 'Backlog',
    zone: backendTask.zone || '',
    activeList: backendTask.active_list || '',
  };
};

// Helper function to transform frontend task to backend format
export const transformFrontendTask = (frontendTask: any): Omit<BackendTask, 's_no'> => {
  return {
    project_zone_no_zone: frontendTask.projectId,
    active_list: frontendTask.activeList || null,
    job_description: frontendTask.description || null,
    expected_hours: frontendTask.expectedHours || null,
    consumed_hours: frontendTask.consumedHours || null,
    zone: frontendTask.zone || null,
    responsible_1: frontendTask.assignedTo || null,
    responsible_2: frontendTask.assignedTo2 || null,
    job_status: frontendTask.status || null,
  };
};

// Project-related API functions derived from task data
export const projectAPI = {
  // Get all unique projects from tasks
  getAllProjects: async (): Promise<string[]> => {
    try {
      const tasks = await taskAPI.getAllTasks();
      const projectIds = Array.from(new Set(
        tasks.map(task => task.project_zone_no_zone).filter(Boolean)
      ));
      return projectIds;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  },

  // Get project details derived from tasks
  getProjectDetails: async (projectId: string) => {
    try {
      const tasks = await taskAPI.getAllTasks();
      const projectTasks = tasks.filter(task => task.project_zone_no_zone === projectId);

      if (projectTasks.length === 0) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Extract project information from tasks
      const zones = Array.from(new Set(projectTasks.map(task => task.zone).filter(Boolean)));
      const assignees = Array.from(new Set([
        ...projectTasks.map(task => task.responsible_1).filter(Boolean),
        ...projectTasks.map(task => task.responsible_2).filter(Boolean)
      ]));

      const totalExpectedHours = projectTasks.reduce((sum, task) => sum + (task.expected_hours || 0), 0);
      const totalConsumedHours = projectTasks.reduce((sum, task) => sum + (task.consumed_hours || 0), 0);

      const statusCounts = projectTasks.reduce((counts, task) => {
        const status = task.job_status || 'Unknown';
        counts[status] = (counts[status] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);

      return {
        id: projectId,
        name: projectId, // Use project ID as name since we don't have separate names
        taskCount: projectTasks.length,
        zones,
        team: assignees,
        totalExpectedHours,
        totalConsumedHours,
        progress: totalExpectedHours > 0 ? (totalConsumedHours / totalExpectedHours) * 100 : 0,
        statusCounts,
        tasks: projectTasks
      };
    } catch (error) {
      console.error(`Error fetching project details for ${projectId}:`, error);
      throw error;
    }
  },

  // Get project statistics
  getProjectStats: async () => {
    try {
      const tasks = await taskAPI.getAllTasks();
      const projectStats: Record<string, any> = {};

      tasks.forEach(task => {
        const projectId = task.project_zone_no_zone;
        if (!projectId) return;

        if (!projectStats[projectId]) {
          projectStats[projectId] = {
            id: projectId,
            name: projectId,
            taskCount: 0,
            expectedHours: 0,
            consumedHours: 0,
            zones: new Set(),
            assignees: new Set(),
            statuses: {}
          };
        }

        const project = projectStats[projectId];
        project.taskCount++;
        project.expectedHours += task.expected_hours || 0;
        project.consumedHours += task.consumed_hours || 0;

        if (task.zone) project.zones.add(task.zone);
        if (task.responsible_1) project.assignees.add(task.responsible_1);
        if (task.responsible_2) project.assignees.add(task.responsible_2);

        const status = task.job_status || 'Unknown';
        project.statuses[status] = (project.statuses[status] || 0) + 1;
      });

      // Convert Sets to Arrays
      Object.values(projectStats).forEach((project: any) => {
        project.zones = Array.from(project.zones);
        project.assignees = Array.from(project.assignees);
        project.progress = project.expectedHours > 0 ? (project.consumedHours / project.expectedHours) * 100 : 0;
      });

      return Object.values(projectStats);
    } catch (error) {
      console.error('Error fetching project stats:', error);
      throw error;
    }
  }
};

export default apiClient;
