import axios from 'axios';

// Backend API base URL
const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Task API interface matching Django backend
export interface BackendTask {
  s_no: number;
  project_zone_no_zone: string;
  active_list: string | null;
  job_description: string | null;
  expected_hours: number | null;
  consumed_hours: number | null;
  zone: string | null;
  responsible_1: string | null;
  responsible_2: string | null;
  job_status: string | null;
}

// API service functions
export const taskAPI = {
  // Get all tasks
  getAllTasks: async (): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get('/tasks/');
      return response.data;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  },

  // Get task by ID
  getTaskById: async (id: number): Promise<BackendTask> => {
    try {
      const response = await apiClient.get(`/tasks/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task ${id}:`, error);
      throw error;
    }
  },

  // Create new task
  createTask: async (taskData: Omit<BackendTask, 's_no'>): Promise<BackendTask> => {
    try {
      const response = await apiClient.post('/tasks/', taskData);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  // Update task
  updateTask: async (id: number, taskData: Partial<BackendTask>): Promise<BackendTask> => {
    try {
      const response = await apiClient.patch(`/tasks/${id}/`, taskData);
      return response.data;
    } catch (error) {
      console.error(`Error updating task ${id}:`, error);
      throw error;
    }
  },

  // Delete task
  deleteTask: async (id: number): Promise<void> => {
    try {
      await apiClient.delete(`/tasks/${id}/`);
    } catch (error) {
      console.error(`Error deleting task ${id}:`, error);
      throw error;
    }
  },

  // Get tasks by zone
  getTasksByZone: async (zone: string): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get(`/tasks/?zone=${zone}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tasks for zone ${zone}:`, error);
      throw error;
    }
  },

  // Get tasks by status
  getTasksByStatus: async (status: string): Promise<BackendTask[]> => {
    try {
      const response = await apiClient.get(`/tasks/?job_status=${status}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tasks with status ${status}:`, error);
      throw error;
    }
  },
};

// Helper function to transform backend task to frontend format
export const transformBackendTask = (backendTask: BackendTask) => {
  return {
    id: backendTask.s_no.toString(),
    projectId: backendTask.project_zone_no_zone,
    assignedTo: backendTask.responsible_1 || '',
    assignedTo2: backendTask.responsible_2 || '',
    description: backendTask.job_description || '',
    expectedHours: backendTask.expected_hours || 0,
    consumedHours: backendTask.consumed_hours || 0,
    status: backendTask.job_status || 'Backlog',
    zone: backendTask.zone || '',
    activeList: backendTask.active_list || '',
  };
};

// Helper function to transform frontend task to backend format
export const transformFrontendTask = (frontendTask: any): Omit<BackendTask, 's_no'> => {
  return {
    project_zone_no_zone: frontendTask.projectId,
    active_list: frontendTask.activeList || null,
    job_description: frontendTask.description || null,
    expected_hours: frontendTask.expectedHours || null,
    consumed_hours: frontendTask.consumedHours || null,
    zone: frontendTask.zone || null,
    responsible_1: frontendTask.assignedTo || null,
    responsible_2: frontendTask.assignedTo2 || null,
    job_status: frontendTask.status || null,
  };
};

export default apiClient;
