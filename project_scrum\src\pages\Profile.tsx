import React from 'react';
import { useAuth } from '../context/AuthContext';
import Avatar from '../components/ui/Avatar';

const Profile = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Please log in to view your profile.</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Your Profile</h1>

      <div className="max-w-2xl bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center space-x-6 mb-6">
          <Avatar src={currentUser.avatar} name={currentUser.name} size="lg" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{currentUser.name}</h2>
            <p className="text-gray-500">{currentUser.email}</p>
            <p className="text-sm text-gray-500 capitalize mt-1">{currentUser.role}</p>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Account Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <input
                  type="text"
                  value={currentUser.name}
                  readOnly
                  className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  value={currentUser.email}
                  readOnly
                  className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Role</label>
                <input
                  type="text"
                  value={currentUser.role}
                  readOnly
                  className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 capitalize"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Security</h3>
            <button className="text-blue-600 hover:text-blue-800">
              Change Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;