from rest_framework import viewsets
from tasks.models import Task
from tasks.api.serializers import JobSerializer
from django.db import connection
from django.http import JsonResponse


DEFAULT_ZONE = 'EIC'

def job_data_view(request):
    with connection.cursor() as cursor:
        cursor.execute("SELECT * FROM tasks_job WHERE zone = %s", [DEFAULT_ZONE])
        rows = cursor.fetchall()

        # Convert result to a list of dicts
        columns = [col[0] for col in cursor.description]
        data = [dict(zip(columns, row)) for row in rows]

    return JsonResponse({'jobs': data})

class JobViewSet(viewsets.ModelViewSet):
    queryset = Task.objects.all()
    serializer_class = JobSerializer


