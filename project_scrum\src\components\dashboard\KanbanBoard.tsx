import React from 'react';
import { Story, Status } from '../../types';
import StoryCard from '../story/StoryCard';

interface KanbanBoardProps {
  stories: Story[];
  onStatusChange?: (storyId: string, newStatus: Status) => void;
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({ stories, onStatusChange }) => {
  const backlogStories = stories.filter(s => s.status === Status.BACKLOG);
  const workingStories = stories.filter(s => s.status === Status.WORKING);
  const checkStories = stories.filter(s => s.status === Status.CHECK);
  const doneStories = stories.filter(s => s.status === Status.DONE);
  
  const handleDragStart = (e: React.DragEvent, storyId: string) => {
    e.dataTransfer.setData('storyId', storyId);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };
  
  const handleDrop = (e: React.DragEvent, newStatus: Status) => {
    e.preventDefault();
    const storyId = e.dataTransfer.getData('storyId');
    
    if (onStatusChange) {
      onStatusChange(storyId, newStatus);
    }
  };
  
  const renderColumn = (title: string, count: number, status: Status, stories: Story[], bgColor: string) => (
    <div 
      className="bg-white shadow-sm rounded-lg"
      onDragOver={handleDragOver}
      onDrop={(e) => handleDrop(e, status)}
    >
      <div className={`p-4 border-b border-gray-200 ${bgColor} rounded-t-lg`}>
        <h3 className="font-medium text-gray-800 truncate">
          {title} ({count})
        </h3>
      </div>
      <div className="p-2 min-h-[200px] max-h-[calc(100vh-300px)] overflow-y-auto">
        {stories.length === 0 ? (
          <div className="h-20 flex items-center justify-center text-gray-400 text-sm">
            No stories
          </div>
        ) : (
          <div className="space-y-2">
            {stories.map(story => (
              <div 
                key={story.id}
                draggable
                onDragStart={(e) => handleDragStart(e, story.id)}
                className="group"
              >
                <StoryCard story={story} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {renderColumn('Backlog', backlogStories.length, Status.BACKLOG, backlogStories, 'bg-gray-50')}
      {renderColumn('Working', workingStories.length, Status.WORKING, workingStories, 'bg-blue-50')}
      {renderColumn('Check', checkStories.length, Status.CHECK, checkStories, 'bg-yellow-50')}
      {renderColumn('Done', doneStories.length, Status.DONE, doneStories, 'bg-green-50')}
    </div>
  );
};

export default KanbanBoard;