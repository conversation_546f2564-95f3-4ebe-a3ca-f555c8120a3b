// import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
// import { AuthProvider } from './context/AuthContext';
// import { ProjectProvider } from './context/ProjectContext';
// import AppLayout from './components/layout/AppLayout';
// import Login from './pages/Login';
// import Signup from './pages/Signup';
// import Dashboard from './pages/Dashboard';
// import Projects from './pages/Projects';
// import NewProject from './pages/NewProject';
// import ProjectDetails from './pages/ProjectDetails';
// // import Backlog from './pages/Backlog';
// // import Sprints from './pages/Sprints';
// // import NewSprint from './pages/NewSprint';
// import Team from './pages/Team';
// import Profile from './pages/Profile';
// import Settings from './pages/Settings';
// import PrivateRoute from './components/auth/PrivateRoute';
// import Board from './pages/Board';

// function App() {
//   return (
//     <Router>
//       <AuthProvider>
//         <ProjectProvider>
//           <Routes>
//             {/* Public routes */}
//             <Route path="/login" element={<Login />} />
//             <Route path="/signup" element={<Signup />} />

//             {/* Protected routes */}
//             <Route element={<PrivateRoute />}>
//               <Route element={<AppLayout />}>
//                 <Route path="/" element={<Navigate to="/dashboard" replace />} />
//                 <Route path="/dashboard" element={<Dashboard />} />
//                 <Route path="/projects" element={<Projects />} />
//                 <Route path="/projects/new" element={<NewProject />} />
//                 <Route path="/projects/:id" element={<ProjectDetails />} />
//                 {/* <Route path="/backlog" element={<Backlog />} /> */}
//                 <Route path="/sprints" element={<Board />} />
//                 {/* <Route path="/sprints/new" element={<NewSprint />} /> */}
//                 <Route path="/team" element={<Team />} />
//                 <Route path="/profile" element={<Profile />} />
//                 <Route path="/settings" element={<Settings />} />
//               </Route>
//             </Route>
//           </Routes>
//         </ProjectProvider>
//       </AuthProvider>
//     </Router>
//   );
// }

// export default App;

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { ProjectProvider } from './context/ProjectContext';
import { TaskProvider } from './context/TaskContext';
import AppLayout from './components/layout/AppLayout';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import ProductionDashboard from './pages/ProductionDashboard';
import Projects from './pages/Projects';
import NewProject from './pages/NewProject';
import ProjectDetails from './pages/ProjectDetails';
import Backlog from './pages/Backlog';
import Sprints from './pages/Sprints';
import NewSprint from './pages/NewSprint';
import Team from './pages/Team';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import PrivateRoute from './components/auth/PrivateRoute';
import Board from './pages/Board';

function App() {
  return (
    <Router>
      <AuthProvider>
        <ProjectProvider>
          <TaskProvider>
            <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />

            {/* Protected routes */}
            <Route element={<PrivateRoute />}>
              <Route element={<AppLayout />}>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<ProductionDashboard />} />
                <Route path="/project-dashboard" element={<Dashboard />} />
                <Route path="/projects" element={<Projects />} />
                <Route path="/projects/new" element={<NewProject />} />
                <Route path="/projects/:id" element={<ProjectDetails />} />
                <Route path="/backlog" element={<Backlog />} />
                <Route path="/sprints" element={<Board />} />
                {/* <Route path="/sprints/new" element={<NewSprint />} /> */}
                <Route path="/team" element={<Team />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/settings" element={<Settings />} />
              </Route>
            </Route>
            </Routes>
          </TaskProvider>
        </ProjectProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
