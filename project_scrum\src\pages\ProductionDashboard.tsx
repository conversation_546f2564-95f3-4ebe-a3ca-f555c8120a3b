// // import React from 'react';
// // import { Calendar } from 'lucide-react';
// // import { useNavigate } from 'react-router-dom';
// // import { useProject } from '../context/ProjectContext';

// // interface ZoneData {
// //   name: string;
// //   percentage: number;
// // }

// // interface JobStatus {
// //   status: string;
// //   count: number;
// // }

// // const ProductionDashboard = () => {
// //   const navigate = useNavigate();
// //   const { projects } = useProject();

// //   // Sample data - in a real app this would come from your backend
// //   const zoneData: ZoneData[] = [
// //     { name: 'L1, 1', percentage: 46 },
// //     { name: 'L2, 3', percentage: 54 },
// //   ];

// //   const jobStatus: JobStatus[] = [
// //     { status: 'Under Working', count: 2 },
// //   ];

// //   // Function to handle project click
// //   const handleProjectClick = (projectId: string) => {
// //     navigate(`/projects/${projectId}`);
// //   };

// //   return (
// //     <div className="p-6">
// //       <div className="grid grid-cols-12 gap-6">
// //         {/* Left Column */}
// //         <div className="col-span-3">
// //           <div className="bg-white rounded-lg shadow p-4">
// //             <div className="flex items-center justify-between mb-4">
// //               <div className="flex items-center">
// //                 <Calendar className="w-5 h-5 mr-2" />
// //                 <span className="font-medium">Create Date</span>
// //               </div>
// //             </div>
// //             <div className="space-y-2">
// //               {['06-12-2024', '02-12-2024', '16-12-2024', '19-12-2024', '23-12-2024'].map((date) => (
// //                 <div key={date} className="bg-blue-100 p-2 rounded">
// //                   {date}
// //                 </div>
// //               ))}
// //             </div>
// //           </div>


// //           <div className="bg-white rounded-lg shadow p-4 mt-6">
// //             <div className="flex items-center justify-between mb-4">
// //               <span className="font-medium">Zone</span>
// //             </div>
// //             <div className="space-y-2">
// //               {[
// //                 { zone: 'Development', names: ['Jayati Vaid'] },
// //                 { zone: 'HW', names: ['Mukesh Chauhan', 'Rajiv Singh','Krishan Kumar'] },
// //                 { zone: 'IZ', names: [''] },
// //                 { zone: 'L1', names: [''] },
// //                 { zone: 'SZ', names: [''] },
// //               ].map(({ zone, names }) => (
// //                 <div key={zone} className="bg-blue-100 p-2 rounded">
// //                   <div className="font-semibold">{zone}</div>
// //                   <ul className="text-sm text-gray-700 list-disc list-inside">
// //                     {names.map((name, idx) => (
// //                       <li key={idx}>{name}</li>
// //                     ))}
// //                   </ul>
// //                 </div>
// //               ))}
// //             </div>
// //           </div>
// //         </div>


// //         {/* Middle Column */}
// //         <div className="col-span-3">
// //           <div className="bg-white rounded-lg shadow p-4">
// //             <div className="flex items-center justify-between mb-4">
// //               <span className="font-medium">Project</span>
// //             </div>
// //             <div className="space-y-2">
// //               {projects.length > 0 ? (
// //                 projects.map((project) => (
// //                   <div
// //                     key={project.id}
// //                     className="p-2 border-b hover:bg-blue-50 cursor-pointer transition-colors"
// //                     onClick={() => handleProjectClick(project.id)}
// //                   >
// //                     {project.name}
// //                   </div>
// //                 ))
// //               ) : (
// //                 [
// //                   'P004976-BMM BF',
// //                   'P005377-TSJ BFS & I-4th Stoves ',
// //                   'P005766-AMNS BF2&BF3 &GAD',
// //                   'P005960-JSW Dolvi-BF3 Stoves',
// //                   'P006006-AMNS Sub lance',
// //                   'P006049-TSJ LD1 Sub lance(HOLD)',
// //                   'P006198-DPS BF3-BF Upgradation',
// //                   'P006205-JSOL Sub lance',
// //                   'P006273-TSM BF2-4th Stoves',
// //                   'P006384-DSP BF3- Stoves upgardation',
// //                   'POO6416-JSW SUB Lance-3',
// //                   'P00XXX-JSL HMDS'
// //                 ].map((projectName) => (
// //                   <div key={projectName} className="p-2 border-b">
// //                     {projectName}
// //                   </div>
// //                 ))
// //               )}
// //             </div>
// //           </div>
// //         </div>

// //         {/* Charts Column */}
// //         <div className="col-span-6">
// //           <div className="grid grid-rows-2 gap-6 h-full">
// //             {/* Zone Wise Total */}
// //             <div className="bg-white rounded-lg shadow p-4">
// //               <h3 className="font-medium mb-4">Zone Wise Total</h3>
// //               <div className="relative h-64">
// //                 <div className="absolute inset-0 flex items-center justify-center">
// //                   <div className="w-48 h-48 relative">
// //                     <div className="w-full h-full rounded-full bg-blue-500" style={{ clipPath: 'polygon(0 0, 54% 0, 54% 100%, 0% 100%)' }} />
// //                     <div className="w-full h-full rounded-full bg-orange-500 absolute top-0" style={{ clipPath: 'polygon(54% 0, 100% 0, 100% 100%, 54% 100%)' }} />
// //                     <div className="absolute inset-0 flex items-center justify-center">
// //                       <div className="text-center">
// //                         <div className="text-sm">L1, 1</div>
// //                         <div className="font-bold">46%</div>
// //                       </div>
// //                     </div>
// //                   </div>
// //                 </div>
// //               </div>
// //             </div>

// //             {/* Job Status */}
// //             <div className="bg-white rounded-lg shadow p-4">
// //               <h3 className="font-medium mb-4">Job Status</h3>
// //               <div className="relative h-64">
// //                 <div className="absolute inset-0 flex items-center justify-center">
// //                   <div className="w-48 h-48 relative">
// //                     <div className="w-full h-full rounded-full bg-green-500" />
// //                     <div className="absolute inset-0 flex items-center justify-center">
// //                       <div className="text-center text-white">
// //                         <div>Under</div>
// //                         <div>Working, 2</div>
// //                       </div>
// //                     </div>
// //                   </div>
// //                 </div>
// //               </div>
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     </div>
// //   );
// // };

// // export default ProductionDashboard;







// import React, { useState } from 'react';
// import { Calendar } from 'lucide-react';
// import { useNavigate } from 'react-router-dom';
// import { useProject } from '../context/ProjectContext';

// interface ZoneData {
//   name: string;
//   percentage: number;
// }

// interface JobStatus {
//   status: string;
//   count: number;
// }

// interface Project {
//   id: string;
//   name: string;
// }

// // Sub-component: ZoneProjectViewer
// const ZoneProjectViewer = () => {
//   const [selectedZone, setSelectedZone] = useState<string | null>(null);
//   const [selectedPerson, setSelectedPerson] = useState<string | null>(null);

//   const data = [
//     { zone: "Development", names: ["Jayati Vaid"] },
//     { zone: "HW", names: ["Mukesh Chauhan", "Rajiv Singh", "Krishan Kumar"] },
//     { zone: "IZ", names: ["Kusum ", "Himanshi","Gagandeep Singh", "Satya Prakash"] },
//     { zone: "L1", names: ["Ananad Mehra","Pradeep Navhal", "Bhuvnesh Kumar", "Anshul Singhal","Harlal Kumar"] },
//     { zone: "SZ", names: ["Deepak Gupta", "Mayank", "Soham", "Sampad", "Binoy"] },
//   ];

//   const personProjects: Record<string, string[]> = {
//     "Mukesh Chauhan": ["P006101", "P006102"],
//     "Rajiv Singh": ["P005766"],
//     "Krishan Kumar": ["P006104", "P006105", "P00106"],
//     "Kusum": ["P006061"],
//     "Himanshi" : ["P005960"],
//   };

//   return (
//     <div className="bg-white rounded-lg shadow p-4 mt-6">
//       <div className="flex items-center justify-between mb-4">
//         <span className="font-medium">Zone</span>
//       </div>
//       <div className="space-y-2">
//         {data.map(({ zone, names }) => (
//           <div
//             key={zone}
//             className="bg-blue-100 p-2 rounded cursor-pointer"
//             onClick={() => {
//               setSelectedZone(selectedZone === zone ? null : zone);
//               setSelectedPerson(null);
//             }}
//           >
//             <div className="font-semibold">{zone}</div>
//             {selectedZone === zone && names.length > 0 && (
//               <ul className="text-sm text-gray-700 list-disc list-inside">
//                 {names.map((name, idx) => (
//                   <li
//                     key={idx}
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       setSelectedPerson(name);
//                     }}
//                     className="cursor-pointer hover:underline"
//                   >
//                     {name}
//                   </li>
//                 ))}
//               </ul>
//             )}
//           </div>
//         ))}
//       </div>

//       {selectedPerson && (
//         <div className="mt-4 bg-gray-100 p-3 rounded">
//           <div className="font-semibold mb-2">{selectedPerson}'s Projects</div>
//           <ul className="list-disc list-inside text-sm text-gray-800">
//             {personProjects[selectedPerson]?.map((project, idx) => (
//               <li key={idx}>{project}</li>
//             )) || <li>No Projects Found</li>}
//           </ul>
//         </div>
//       )}
//     </div>
//   );
// };

// // Main Component: ProductionDashboard
// const ProductionDashboard = () => {
//   const navigate = useNavigate();
//   const { projects } = useProject();

//   const zoneData: ZoneData[] = [
//     { name: 'L1, 1', percentage: 46 },
//     { name: 'L2, 3', percentage: 54 },
//   ];

//   const jobStatus: JobStatus[] = [
//     { status: 'Under Working', count: 2 },
//   ];

//   const handleProjectClick = (projectId: string) => {
//     navigate(`/projects/${projectId}`);
//   };

//   return (
//     <div className="p-6">
//       <div className="grid grid-cols-12 gap-6">
//         {/* Left Column */}
//         <div className="col-span-3">

//           {/* <!-- create date   in column --> */}
//           {/* <div className="bg-white rounded-lg shadow p-4">
//             <div className="flex items-center justify-between mb-4">
//               <div className="flex items-center">
//                 <Calendar className="w-5 h-5 mr-2" />
//                 <span className="font-medium">Create Date</span>
//               </div>
//             </div>
//             <div className="space-y-2">
//               {['06-12-2024', '02-12-2024', '16-12-2024', '19-12-2024', '23-12-2024'].map((date) => (
//                 <div key={date} className="bg-blue-100 p-2 rounded">
//                   {date}
//                 </div>
//               ))}
//             </div>
//           </div> */}


//           {/* <!-- Total Projects and Remaining Projects --> */}
//           {/* <div className="bg-white rounded-lg shadow p-4">
//   <div className="grid grid-cols-2 gap-4">
//     <div className="bg-pink-300 text-center p-4 rounded-lg shadow">
//       <div className="text-sm text-gray-600">Total Projects</div>
//       <div className="text-2xl font-bold text-pink-700">
//         {projects.length}
//       </div>
//     </div>

//     <div className="bg-pink-300 text-center p-4 rounded-lg shadow">
//       <div className="text-sm text-gray-600">Remaining Projects</div>
//       <div className="text-2xl font-bold text-pink-700">
//         {
//           // Assuming each project has a `completed` boolean:
//           projects.filter((project: any) => !project.completed).length
//         }
//       </div>
//     </div>
//   </div>
// </div> */}
//   {/* <!-- here total number of project is end --> */}



//   {/* <!-- create total project and remaining project in rows --> */}
//   <div className="bg-white rounded-lg shadow p-4">
//   <div className="grid grid-cols-1 gap-y-4">
//     {/* Total Projects Box */}
//     <div className="bg-pink-300 text-center p-4 rounded-lg shadow">
//       <div className="text-sm text-gray-600">Total Projects</div>
//       <div className="text-2xl font-bold text-pink-700">
//         {projects.length}
//       </div>
//     </div>

//     {/* Remaining Projects Box */}
//     <div className="bg-pink-300 text-center p-4 rounded-lg shadow">
//       <div className="text-sm text-gray-600">Remaining Projects</div>
//       <div className="text-2xl font-bold text-pink-700">
//         {
//           // Assuming each project has a `completed` boolean:
//           projects.filter((project: any) => !project.completed).length
//         }
//       </div>
//     </div>
//   </div>
// </div>
//   {/* <!-- here total number of project is end --> */}



//           <ZoneProjectViewer />
//         </div>

//         {/* Middle Column */}
//         <div className="col-span-3">
//           <div className="bg-white rounded-lg shadow p-4">
//             <div className="flex items-center justify-between mb-4">
//               <span className="font-medium">Project</span>
//             </div>
//             <div className="space-y-2">
//               {projects.length > 0 ? (
//                 projects.map((project: Project) => (
//                   <div
//                     key={project.id}
//                     className="p-2 border-b hover:bg-blue-50 cursor-pointer transition-colors"
//                     onClick={() => handleProjectClick(project.id)}
//                   >
//                     {project.name}
//                   </div>
//                 ))
//               ) : (
//                 [
//                   'P004976-BMM BF',
//                   'P005377-TSJ BFS & I-4th Stoves ',
//                   'P005766-AMNS BF2&BF3 &GAD',
//                   'P005960-JSW Dolvi-BF3 Stoves',
//                   'P006006-AMNS Sub lance',
//                   'P006049-TSJ LD1 Sub lance(HOLD)',
//                   'P006198-DPS BF3-BF Upgradation',
//                   'P006205-JSOL Sub lance',
//                   'P006273-TSM BF2-4th Stoves',
//                   'P006384-DSP BF3- Stoves upgardation',
//                   'POO6416-JSW SUB Lance-3',
//                   'P00XXX-JSL HMDS'
//                 ].map((projectName) => (
//                   <div key={projectName} className="p-2 border-b">
//                     {projectName}
//                   </div>
//                 ))
//               )}
//             </div>
//           </div>
//         </div>

//         {/* Charts Column */}
//         <div className="col-span-6">
//           <div className="grid grid-rows-2 gap-6 h-full">
//             {/* Zone Wise Total */}
//             <div className="bg-white rounded-lg shadow p-4">
//               <h3 className="font-medium mb-4">Zone Wise Total</h3>
//               <div className="relative h-64">
//                 <div className="absolute inset-0 flex items-center justify-center">
//                   <div className="w-48 h-48 relative">
//                     <div
//                       className="w-full h-full rounded-full bg-blue-500"
//                       style={{ clipPath: 'polygon(0 0, 54% 0, 54% 100%, 0% 100%)' }}
//                     />
//                     <div
//                       className="w-full h-full rounded-full bg-orange-500 absolute top-0"
//                       style={{ clipPath: 'polygon(54% 0, 100% 0, 100% 100%, 54% 100%)' }}
//                     />
//                     <div className="absolute inset-0 flex items-center justify-center">
//                       <div className="text-center">
//                         <div className="text-sm">L1, 1</div>
//                         <div className="font-bold">46%</div>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>

//             {/* Job Status */}
//             <div className="bg-white rounded-lg shadow p-4">
//               <h3 className="font-medium mb-4">Job Status</h3>
//               <div className="relative h-64">
//                 <div className="absolute inset-0 flex items-center justify-center">
//                   <div className="w-48 h-48 relative">
//                     <div className="w-full h-full rounded-full bg-green-500" />
//                     <div className="absolute inset-0 flex items-center justify-center">
//                       <div className="text-center text-white">
//                         <div>Under</div>
//                         <div>Working, 2</div>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>

//       </div>
//     </div>
//   );
// };

// export default ProductionDashboard;







import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../context/ProjectContext';
import { useTaskData } from '../hooks/useTaskData';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

interface TeamMemberData {
  name: string;
  sprints: number;
  zone: string;
}

interface Project {
  id: string;
  name: string;
  status?: string;
}

interface ZoneData {
  zone: string;
  names: string[];
}


// Define zone data at the top level so it can be shared
const zoneData: ZoneData[] = [
  { zone: "Development", names: ["Jayati Vaid"] },
  { zone: "HW", names: ["Mukesh Chauhan", "Rajiv Singh", "Krishan Kumar"] },
  { zone: "IZ", names: ["Kusum", "Himanshi", "Gagandeep Singh", "Satya Prakash"] },
  { zone: "L1", names: ["Anand Mehra", "Pradeep Navhal", "Bhuvnesh Kumar", "Anshul Singhal", "Harlal Kumar"] },
  { zone: "SZ", names: ["Deepak Gupta", "Mayank", "Soham", "Sampad", "Binoy"] },
];

// Define sprint data for each team member
const teamSprintData: TeamMemberData[] = [
  { name: "Jayati Vaid", sprints: 3, zone: "Development" },
  { name: "Mukesh Chauhan", sprints: 5, zone: "HW" },
  { name: "Rajiv Singh", sprints: 2, zone: "HW" },
  { name: "Krishan Kumar", sprints: 4, zone: "HW" },
  { name: "Kusum", sprints: 1, zone: "IZ" },
  { name: "Himanshi", sprints: 3, zone: "IZ" },
  { name: "Gagandeep Singh", sprints: 2, zone: "IZ" },
  { name: "Satya Prakash", sprints: 3, zone: "IZ" },
  { name: "Anand Mehra", sprints: 2, zone: "L1" },
  { name: "Pradeep Navhal", sprints: 4, zone: "L1" },
  { name: "Bhuvnesh Kumar", sprints: 3, zone: "L1" },
  { name: "Anshul Singhal", sprints: 2, zone: "L1" },
  { name: "Harlal Kumar", sprints: 1, zone: "L1" },
  { name: "Deepak Gupta", sprints: 4, zone: "SZ" },
  { name: "Mayank", sprints: 2, zone: "SZ" },
  { name: "Soham", sprints: 3, zone: "SZ" },
  { name: "Sampad", sprints: 1, zone: "SZ" },
  { name: "Binoy", sprints: 2, zone: "SZ" },
];

interface ZoneProjectViewerProps {
  selectedZone: string | null;
  setSelectedZone: React.Dispatch<React.SetStateAction<string | null>>;
  tasks: any[];
}

// Sub-component: ZoneProjectViewer
const ZoneProjectViewer = ({ selectedZone, setSelectedZone, tasks }: ZoneProjectViewerProps) => {
  const [selectedPersons, setSelectedPersons] = useState<string[]>([]);

  // Generate person projects from real task data
  const getPersonProjects = (): Record<string, string[]> => {
    const personProjects: Record<string, string[]> = {};

    tasks.forEach(task => {
      const assignee1 = task.assignedTo;
      const assignee2 = task.assignedTo2;
      const projectId = task.projectId;

      if (assignee1 && projectId) {
        if (!personProjects[assignee1]) {
          personProjects[assignee1] = [];
        }
        if (!personProjects[assignee1].includes(projectId)) {
          personProjects[assignee1].push(projectId);
        }
      }

      if (assignee2 && projectId) {
        if (!personProjects[assignee2]) {
          personProjects[assignee2] = [];
        }
        if (!personProjects[assignee2].includes(projectId)) {
          personProjects[assignee2].push(projectId);
        }
      }
    });

    return personProjects;
  };

  const personProjects = getPersonProjects();

  // Function to toggle a person in the selected persons array
  const togglePersonSelection = (name: string) => {
    setSelectedPersons(prev => {
      if (prev.includes(name)) {
        return prev.filter(p => p !== name);
      } else {
        return [...prev, name];
      }
    });
  };

  // Get all projects for selected persons
  const getAllSelectedProjects = () => {
    const allProjects: string[] = [];
    selectedPersons.forEach(person => {
      if (personProjects[person]) {
        personProjects[person].forEach(project => {
          if (!allProjects.includes(project)) {
            allProjects.push(project);
          }
        });
      }
    });
    return allProjects;
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 mt-6">
      <div className="bg-blue-300 text-center p-4 rounded-lg shadow mb-4">
        <div className="text-sm text-gray-600 bg-white px-3 py-1 rounded-md inline-block mb-1">Team</div>
        <div className="text-xl font-bold text-teal-700">
          {selectedZone || "All Team"}
        </div>
      </div>
      <div className="space-y-2">
        {zoneData.map(({ zone, names }) => (
          <div
            key={zone}
            className={`p-2 rounded cursor-pointer ${selectedZone === zone ? 'bg-blue-300' : 'bg-blue-100'}`}
            onClick={() => {
              setSelectedZone(selectedZone === zone ? null : zone);
              setSelectedPersons([]);
            }}
          >
            <div className="font-semibold">{zone}</div>
            {selectedZone === zone && names.length > 0 && (
              <ul className="text-sm text-gray-700 list-disc list-inside">
                {names.map((name, idx) => (
                  <li
                    key={idx}
                    onClick={(e) => {
                      e.stopPropagation();
                      togglePersonSelection(name);
                    }}
                    className={`cursor-pointer hover:underline ${selectedPersons.includes(name) ? 'font-bold text-blue-800' : ''}`}
                  >
                    {name} {selectedPersons.includes(name) && '✓'}
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>

      {selectedPersons.length > 0 && (
        <div className="mt-4">
          <div className="bg-indigo-300 text-center p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600 bg-white px-3 py-1 rounded-md inline-block mb-1">
              {selectedPersons.length === 1 ? `${selectedPersons[0]}'s Projects` : `${selectedPersons.length} Team Members' Projects`}
            </div>
            <div className="text-xl font-bold text-indigo-700">
              {getAllSelectedProjects().length}
            </div>
          </div>

          <div className="mt-3 bg-white p-3 rounded-lg shadow">
            {selectedPersons.length > 1 && (
              <div className="mb-2 text-sm font-medium text-gray-600">
                Selected team members: {selectedPersons.join(', ')}
              </div>
            )}
            <ul className="list-disc list-inside text-sm text-gray-800">
              {getAllSelectedProjects().length > 0 ? (
                getAllSelectedProjects().map((project, idx) => (
                  <li key={idx} className="py-1 border-b border-gray-100">{project}</li>
                ))
              ) : (
                <li className="py-1 italic text-gray-500">No Projects Found</li>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

// Main Component: ProductionDashboard
const ProductionDashboard = () => {
  const navigate = useNavigate();
  const { projects } = useProject();
  const { tasks, loading, error } = useTaskData();
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  const handleProjectClick = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg">Loading tasks...</div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    );
  }

  // Filter tasks based on selected zone
  const filteredTasks = selectedZone
    ? tasks.filter(task => task.zone === selectedZone)
    : tasks;

  // Calculate status counts for bar chart based on filtered tasks
  const statusCounts: Record<string, number> = {
    Backlog: 0,
    Working: 0,
    Check: 0,
    Done: 0,
  };

  // Count tasks by status
  filteredTasks.forEach(task => {
    const status = task.status || 'Backlog';
    if (statusCounts[status] !== undefined) {
      statusCounts[status]++;
    } else {
      // Handle other statuses that might come from backend
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    }
  });

  const sprintStatusData = Object.entries(statusCounts).map(([status, count]) => ({
    name: status,
    count,
  }));

  // Calculate team task load based on real data
  const getTeamTaskData = () => {
    const teamTaskCounts: Record<string, { name: string; tasks: number; zone: string }> = {};

    filteredTasks.forEach(task => {
      const assignee = task.assignedTo || 'Unassigned';
      const zone = task.zone || 'Unknown';

      if (!teamTaskCounts[assignee]) {
        teamTaskCounts[assignee] = { name: assignee, tasks: 0, zone };
      }
      teamTaskCounts[assignee].tasks++;
    });

    return Object.values(teamTaskCounts);
  };

  const filteredTeamTaskData = getTeamTaskData();

  return (
    


    <div className="p-6">
      <h1 className='text-2xl font-bold mb-6'>Dashboard</h1>

      {/* API Test Component - Remove after testing */}
      <div className="mb-6">
        <ApiTest />
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Left Column */}
        <div className="col-span-3">
          {/* Status Boxes */}
          <div className="bg-white rounded-lg shadow p-4">
            <div className="grid grid-cols-1 gap-y-4">
              <div className="bg-pink-300 text-center p-4 rounded-lg shadow">
                <div className="text-sm text-gray-600">
                  Total Tasks
                  {selectedZone && <span className="ml-1 text-xs">({selectedZone})</span>}
                </div>
                <div className="text-2xl font-bold text-pink-700">
                  {filteredTasks.length}
                </div>
              </div>

              <div className="bg-red-300 text-center p-4 rounded-lg shadow">
                <div className="text-sm text-gray-600">Backlogs</div>
                <div className="text-2xl font-bold text-red-700">
                  {statusCounts.Backlog}
                </div>
              </div>

              <div className="bg-yellow-300 text-center p-4 rounded-lg shadow">
                <div className="text-sm text-gray-600">Working</div>
                <div className="text-2xl font-bold text-yellow-700">
                  {statusCounts.Working}
                </div>
              </div>

              <div className="bg-purple-300 text-center p-4 rounded-lg shadow">
                <div className="text-sm text-gray-600">Check</div>
                <div className="text-2xl font-bold text-purple-700">
                  {statusCounts.Check}
                </div>
              </div>

              <div className="bg-green-300 text-center p-4 rounded-lg shadow">
                <div className="text-sm text-gray-600">Done</div>
                <div className="text-2xl font-bold text-green-700">
                  {statusCounts.Done}
                </div>
              </div>
            </div>
          </div>

          <ZoneProjectViewer selectedZone={selectedZone} setSelectedZone={setSelectedZone} tasks={tasks} />
        </div>

        {/* Middle Column */}
        <div className="col-span-3">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="bg-sky-300 text-center p-4 rounded-lg shadow mb-4">
              <div className="text-sm text-gray-600 bg-white px-3 py-1 rounded-md inline-block mb-1">Total Projects</div>
              <div className="text-2xl font-bold text-sky-700">
                {projects.length > 0 ? projects.length : 12}
              </div>
            </div>
            <div className="space-y-2">
              {projects.length > 0 ? (
                projects.map((project: Project) => (
                  <div
                    key={project.id}
                    className="p-2 border-b hover:bg-blue-50 cursor-pointer transition-colors"
                    onClick={() => handleProjectClick(project.id)}
                  >
                    {project.name}
                  </div>
                ))
              ) : (
                [
                  'P004976-BMM BF',
                  'P005377-TSJ BFS & I-4th Stoves ',
                  'P005766-AMNS BF2&BF3 &GAD',
                  'P005960-JSW Dolvi-BF3 Stoves',
                  'P006006-AMNS Sub lance',
                  'P006049-TSJ LD1 Sub lance(HOLD)',
                  'P006198-DPS BF3-BF Upgradation',
                  'P006205-JSOL Sub lance',
                  'P006273-TSM BF2-4th Stoves',
                  'P006384-DSP BF3- Stoves upgardation',
                  'POO6416-JSW SUB Lance-3',
                  'P00XXX-JSL HMDS'
                ].map((projectName) => (
                  <div key={projectName} className="p-2 border-b">
                    {projectName}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Charts Column */}
        <div className="col-span-6">
          <div className="grid grid-rows-2 gap-6 h-full">
            {/* Team Task Load */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-medium mb-4">
                Team Task Load
                {selectedZone && <span className="ml-2 text-sm text-blue-600">({selectedZone})</span>}
              </h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    layout="vertical"
                    data={filteredTeamTaskData}
                    margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      type="number"
                      label={{ value: "No. of Tasks", position: "insideBottom", offset: -5 }}
                      domain={[0, 'dataMax + 1']}
                    />
                    <YAxis type="category" dataKey="name" width={80} />
                    <Tooltip formatter={(value) => [`${value} Tasks`, 'Load']} />
                    <Bar dataKey="tasks" fill="#8884d8" barSize={20} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              {!selectedZone && (
                <div className="text-center mt-2 text-sm text-gray-500">
                  Click on a Zone to filter team members
                </div>
              )}
            </div>

            {/* Task Status Bar Chart */}
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="font-medium mb-4">
                Task Status
                {selectedZone && <span className="ml-2 text-sm text-blue-600">({selectedZone})</span>}
              </h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={sprintStatusData}
                    margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis allowDecimals={false} />
                    <Tooltip />
                    <Bar dataKey="count" fill="#6366f1" radius={[6, 6, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductionDashboard;
