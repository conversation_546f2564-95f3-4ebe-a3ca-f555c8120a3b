
// import React, { useEffect, useState } from "react";

// type ModalProps = {
//   taskId: string;
//   stages: string[];
//   onClose: () => void;
// };

// type StageData = {
//   stage: string;
//   startDate: string;
//   endDate: string;
// };

// const Modal: React.FC<ModalProps> = ({ taskId, stages, onClose }) => {
//   const localStorageKey = `task-stage-data-${taskId}`;

//   const loadStageData = (): StageData[] => {
//     const savedData = localStorage.getItem(localStorageKey);
//     if (savedData) {
//       try {
//         return JSON.parse(savedData);
//       } catch {
//         // fallback if parsing fails
//         return stages.map((stage) => ({ stage, startDate: "", endDate: "" }));
//       }
//     }
//     return stages.map((stage) => ({ stage, startDate: "", endDate: "" }));
//   };

//   const [stageData, setStageData] = useState<StageData[]>(loadStageData);

//   const handleDateChange = (index: number, type: "startDate" | "endDate", value: string) => {
//     const updatedStageData = [...stageData];
//     updatedStageData[index][type] = value;
//     setStageData(updatedStageData);
//     localStorage.setItem(localStorageKey, JSON.stringify(updatedStageData));
//   };

//   useEffect(() => {
//     // Sync with localStorage on initial render
//     const stored = localStorage.getItem(localStorageKey);
//     if (stored) {
//       try {
//         setStageData(JSON.parse(stored));
//       } catch {
//         // If error, don't crash the app
//         console.warn("Could not parse saved stage data");
//       }
//     }
//   }, [taskId]);

//   return (
//     <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex justify-center items-center z-50">
//       <div className="bg-white p-6 rounded-md shadow-md max-w-4xl w-full overflow-auto max-h-[90vh]">
//         <div className="text-center">
//           <p className="font-semibold text-xl mb-4">Task Stages for {taskId}</p>

//           <table className="min-w-full table-auto border-collapse">
//             <thead>
//               <tr>
//                 <th className="border border-gray-300 px-4 py-2 text-center">Stage</th>
//                 <th className="border border-gray-300 px-4 py-2 text-center">Start Date</th>
//                 <th className="border border-gray-300 px-4 py-2 text-center">End Date</th>
//               </tr>
//             </thead>
//             <tbody>
//               {stageData.map((stage, index) => (
//                 <tr key={stage.stage}>
//                   <td className="border border-gray-300 px-4 py-2">{stage.stage}</td>
//                   <td className="border border-gray-300 px-4 py-2">
//                     <input
//                       type="date"
//                       value={stage.startDate}
//                       onChange={(e) => handleDateChange(index, "startDate", e.target.value)}
//                       className="border border-gray-300 p-1 rounded w-full"
//                     />
//                   </td>
//                   <td className="border border-gray-300 px-4 py-2">
//                     <input
//                       type="date"
//                       value={stage.endDate}
//                       onChange={(e) => handleDateChange(index, "endDate", e.target.value)}
//                       className="border border-gray-300 p-1 rounded w-full"
//                     />
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>

//           <div className="mt-4">
//             <button
//               onClick={onClose}
//               className="bg-red-500 text-white px-4 py-1 rounded hover:bg-red-600 transition"
//             >
//               Close
//             </button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Modal;







import React, { useEffect, useState } from "react";

type ModalProps = {
  taskId: string;
  stages: string[];
  onClose: () => void;
};

type StageData = {
  stage: string;
  startDate: string;
  endDate: string;
};

const Modal: React.FC<ModalProps> = ({ taskId, stages, onClose }) => {
  const localStorageKey = `task-stage-data-${taskId}`;
  const metaStorageKey = `task-stage-meta-${taskId}`;

  const loadStageData = (): StageData[] => {
    const savedData = localStorage.getItem(localStorageKey);
    if (savedData) {
      try {
        return JSON.parse(savedData);
      } catch {
        return stages.map((stage) => ({ stage, startDate: "", endDate: "" }));
      }
    }
    return stages.map((stage) => ({ stage, startDate: "", endDate: "" }));
  };

  const loadMetaData = () => {
    const saved = localStorage.getItem(metaStorageKey);
    return saved
      ? JSON.parse(saved)
      : {
          totalExpectedHours: "",
          totalConsumedHours: "",
          reason: "",
        };
  };

  const [stageData, setStageData] = useState<StageData[]>(loadStageData);
  const [meta, setMeta] = useState<{
    totalExpectedHours: string;
    totalConsumedHours: string;
    reason: string;
  }>(loadMetaData);

  const updateMeta = (field: string, value: string) => {
    const updated = { ...meta, [field]: value.replace(/\D/g, "") };
    setMeta(updated);
    localStorage.setItem(metaStorageKey, JSON.stringify(updated));
  };

  const updateReason = (value: string) => {
    const updated = { ...meta, reason: value };
    setMeta(updated);
    localStorage.setItem(metaStorageKey, JSON.stringify(updated));
  };

  const handleDateChange = (
    index: number,
    type: "startDate" | "endDate",
    value: string
  ) => {
    const updatedStageData = [...stageData];
    updatedStageData[index][type] = value;
    setStageData(updatedStageData);
    localStorage.setItem(localStorageKey, JSON.stringify(updatedStageData));
  };

  const handleClose = () => {
    const consumed = parseInt(meta.totalConsumedHours || "0", 10);
    const expected = parseInt(meta.totalExpectedHours || "0", 10);
    if (consumed > expected && meta.reason.trim() === "") {
      alert("Please enter a reason for exceeding the expected hours.");
      return;
    }
    onClose();
  };

  useEffect(() => {
    const storedStage = localStorage.getItem(localStorageKey);
    if (storedStage) {
      try {
        setStageData(JSON.parse(storedStage));
      } catch {
        console.warn("Could not parse saved stage data");
      }
    }

    const storedMeta = localStorage.getItem(metaStorageKey);
    if (storedMeta) {
      try {
        setMeta(JSON.parse(storedMeta));
      } catch {
        console.warn("Could not parse saved meta data");
      }
    }
  }, [taskId]);

  const consumed = parseInt(meta.totalConsumedHours || "0", 10);
  const expected = parseInt(meta.totalExpectedHours || "0", 10);
  const shouldShowReason = consumed > expected;

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-md shadow-md max-w-4xl w-full overflow-auto max-h-[90vh]">
        <div className="text-center">
          <p className="font-semibold text-xl mb-4">Task Stages</p>

          <div className="grid grid-cols-2 gap-4 mb-4 text-left">
            <div>
              <label className="block font-medium mb-1">Total Expected Hours:</label>
              <input
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                value={meta.totalExpectedHours}
                onChange={(e) => updateMeta("totalExpectedHours", e.target.value)}
                className="border border-gray-300 rounded p-2 w-full"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">Total Consumed Hours:</label>
              <input
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                value={meta.totalConsumedHours}
                onChange={(e) => updateMeta("totalConsumedHours", e.target.value)}
                className="border border-gray-300 rounded p-2 w-full"
              />
            </div>
          </div>

          {shouldShowReason && (
            <div className="mb-4 text-left">
              <label className="block font-medium mb-1 text-red-600">
                Reason for exceeding hours:
              </label>
              <input
                type="text"
                value={meta.reason}
                onChange={(e) => updateReason(e.target.value)}
                className="border border-gray-300 rounded p-2 w-full"
              />
            </div>
          )}

          <table className="min-w-full table-auto border-collapse mb-4">
            <thead>
              <tr>
                <th className="border border-gray-300 px-4 py-2 text-center">Stage</th>
                <th className="border border-gray-300 px-4 py-2 text-center">Start Date</th>
                <th className="border border-gray-300 px-4 py-2 text-center">End Date</th>
              </tr>
            </thead>
            <tbody>
              {stageData.map((stage, index) => (
                <tr key={stage.stage}>
                  <td className="border border-gray-300 px-4 py-2">{stage.stage}</td>
                  <td className="border border-gray-300 px-4 py-2">
                    <input
                      type="date"
                      value={stage.startDate}
                      onChange={(e) => handleDateChange(index, "startDate", e.target.value)}
                      className="border border-gray-300 p-1 rounded w-full"
                    />
                  </td>
                  <td className="border border-gray-300 px-4 py-2">
                    <input
                      type="date"
                      value={stage.endDate}
                      onChange={(e) => handleDateChange(index, "endDate", e.target.value)}
                      className="border border-gray-300 p-1 rounded w-full"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div>
            <button
              onClick={handleClose}
              className="bg-red-500 text-white px-4 py-1 rounded hover:bg-red-600 transition"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
