import React from 'react';
import { useProject } from '../context/ProjectContext';
import StoryCard from '../components/story/StoryCard';
import { Plus } from 'lucide-react';

const Backlog = () => {
  const { currentProject, getBacklogStories } = useProject();
  const backlogStories = currentProject ? getBacklogStories(currentProject.id) : [];

  if (!currentProject) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Please select a project to view its backlog.</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Project Backlog</h1>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
        >
          <Plus className="w-5 h-5 mr-2" />
          Add Story
        </button>
      </div>

      <div className="space-y-4">
        {backlogStories.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg">
            <p className="text-gray-600">No stories in the backlog.</p>
          </div>
        ) : (
          backlogStories.map(story => (
            <StoryCard key={story.id} story={story} />
          ))
        )}
      </div>
    </div>
  );
};

export default Backlog;