import React from 'react';
import { Priority, Status } from '../../types';

interface BadgeProps {
  variant: 'priority' | 'status' | 'default';
  value: string;
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({ variant, value, className }) => {
  let baseClasses = 'px-2 py-1 text-xs font-semibold rounded-full inline-flex items-center justify-center';
  let variantClasses = '';
  
  if (variant === 'priority') {
    switch (value as Priority) {
      case Priority.LOW:
        variantClasses = 'bg-blue-100 text-blue-800';
        break;
      case Priority.MEDIUM:
        variantClasses = 'bg-yellow-100 text-yellow-800';
        break;
      case Priority.HIGH:
        variantClasses = 'bg-orange-100 text-orange-800';
        break;
      case Priority.CRITICAL:
        variantClasses = 'bg-red-100 text-red-800';
        break;
      default:
        variantClasses = 'bg-gray-100 text-gray-800';
    }
  } else if (variant === 'status') {
    switch (value as Status) {
      case Status.BACKLOG:
        variantClasses = 'bg-gray-100 text-gray-800';
        break;
      case Status.TODO:
        variantClasses = 'bg-purple-100 text-purple-800';
        break;
      case Status.IN_PROGRESS:
        variantClasses = 'bg-blue-100 text-blue-800';
        break;
      case Status.DONE:
        variantClasses = 'bg-green-100 text-green-800';
        break;
      default:
        variantClasses = 'bg-gray-100 text-gray-800';
    }
  } else {
    variantClasses = 'bg-gray-100 text-gray-800';
  }
  
  return (
    <span className={`${baseClasses} ${variantClasses} ${className || ''}`}>
      {value}
    </span>
  );
};

export default Badge;