import React from 'react';

interface AvatarProps {
  src?: string;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Avatar: React.FC<AvatarProps> = ({ 
  src, 
  name = '', // Provide default empty string
  size = 'md',
  className = '' 
}) => {
  // Return null if name is missing or empty
  if (!name?.trim()) {
    return null;
  }

  const initials = name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
    
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
  };
  
  const defaultBgColors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-pink-500',
    'bg-purple-500',
    'bg-indigo-500',
    'bg-red-500',
    'bg-orange-500',
    'bg-teal-500',
  ];
  
  // Determine background color based on name (consistent for same name)
  const colorIndex = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % defaultBgColors.length;
  const bgColor = defaultBgColors[colorIndex];
  
  if (src) {
    return (
      <img 
        src={src} 
        alt={name}
        className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      />
    );
  }
  
  return (
    <div 
      className={`${sizeClasses[size]} ${bgColor} text-white rounded-full flex items-center justify-center font-medium ${className}`}
      title={name}
    >
      {initials}
    </div>
  );
};

export default Avatar;