

import { useState } from "react";
import { DragDropContext, DropResult } from "@hello-pangea/dnd";
import Column from "./Column";

// Define the Task type
export interface Task {
  id: string;
  projectId: string;
  assignedTo: string;
  description: string;
  expectedHours: number | string;
  status: string; // NEW
}

// Define the Columns structure
export interface Columns {
  [key: string]: Task[];
}

// Initial column data
export const initialData: Columns = {
  todo: [
    {
      id: "task-1",
      projectId: "P005960",
      assignedTo: "Kusum",
      description: "Power and Control Cable List",
      expectedHours: 8,
      status: "Backlog",
    },
    {
      id: "task-2",
      projectId: "P005960",
      assignedTo: "<PERSON>han",
      description: "Hyd. Valves MCC and ECD Scheme Drawing",
      expectedHours: 4,
      status: "Backlog",
    },
  ],
  inProgress: [
    {
      id: "task-3",
      projectId: "P005766",
      assignedTo: "<PERSON>han",
      description: "Hookup Drawing",
      expectedHours: 62,
      status: "Working",
    },
  ],
  check: [
    {
      id: "task-4",
      projectId: "P006049",
      assignedTo: "Sampad",
      description: "Approved as Noted Document submission to TATA",
      expectedHours: 1,
      status: "Check",
    },
  ],
  done: [
    {
      id: "task-5",
      projectId: "P005766",
      assignedTo: "Himanshi",
      description: "Flow Switch - Toshbro Vendor drawing",
      expectedHours: 1,
      status: "Done",
    },
  ],
};

function Board(): JSX.Element {
  const [columns, setColumns] = useState<Columns>(initialData);
  const [newTask, setNewTask] = useState<Omit<Task, "id" | "status">>({
    projectId: "",
    assignedTo: "",
    description: "",
    expectedHours: "",
  });

  const updateTask = (taskId: string, updatedTask: Partial<Task>) => {
    const updatedColumns = { ...columns };

    Object.keys(updatedColumns).forEach((columnId) => {
      const column = updatedColumns[columnId];
      const taskIndex = column.findIndex((task) => task.id === taskId);
      if (taskIndex !== -1) {
        column[taskIndex] = { ...column[taskIndex], ...updatedTask };
      }
    });

    setColumns(updatedColumns);
  };

  const deleteTask = (taskId: string, columnId: string) => {
    const updatedColumns = { ...columns };
    updatedColumns[columnId] = updatedColumns[columnId].filter(
      (task) => task.id !== taskId
    );
    setColumns(updatedColumns);
  };

  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;
  
    if (!destination) return;
  
    const sourceColumnId = source.droppableId;
    const destColumnId = destination.droppableId;
  
    if (sourceColumnId === destColumnId && source.index === destination.index) {
      return; // No change
    }
  
    const sourceColumn = Array.from(columns[sourceColumnId]);
    const destColumn = Array.from(columns[destColumnId]);
    const [movedTask] = sourceColumn.splice(source.index, 1);
  
    const getStatusLabel = (columnId: string) => {
      if (columnId === "todo") return "Backlog";
      if (columnId === "inProgress") return "Working";
      if (columnId === "check") return "Check";
      if (columnId === "done") return "Done";
      return columnId.charAt(0).toUpperCase() + columnId.slice(1);
    };
  
    const isBackwardMove = () => {
      const order = ["todo", "inProgress", "check", "done"];
      return order.indexOf(destColumnId) < order.indexOf(sourceColumnId);
    };
  
    const newStatus = isBackwardMove()
      ? `Re ${getStatusLabel(destColumnId)}`
      : getStatusLabel(destColumnId);
  
    const updatedTask = { ...movedTask, status: newStatus };
  
    destColumn.splice(destination.index, 0, updatedTask);
  
    setColumns({
      ...columns,
      [sourceColumnId]: sourceColumn,
      [destColumnId]: destColumn,
    });
  };
  

  const handleAddTaskClick = (columnId: string) => {
    if (
      !newTask.assignedTo ||
      !newTask.description ||
      newTask.expectedHours === ""
    ) {
      alert("Please fill all fields");
      return;
    }

    const expectedHoursValue = Number(newTask.expectedHours);
    if (isNaN(expectedHoursValue)) {
      alert("Expected Hours must be a valid number");
      return;
    }

    const taskId = `task-${Date.now()}`;
    const status =
      columnId === "B"
        ? "Backlog"
        : columnId === "inProgress"
        ? "Working"
        : columnId.charAt(0).toUpperCase() + columnId.slice(1);

    const task: Task = {
      id: taskId,
      assignedTo: newTask.assignedTo,
      description: newTask.description,
      expectedHours: expectedHoursValue,
      projectId: newTask.projectId,
      status,
    };

    const updatedColumns = { ...columns };
    updatedColumns[columnId].push(task);
    setColumns(updatedColumns);

    setNewTask({
      projectId: "",
      assignedTo: "",
      description: "",
      expectedHours: "",
    });
  };

  return (
    <div className="flex flex-col items-start p-6">
      <div className="mb-5 ml-14">
        <input
          type="text"
          placeholder="Project ID"
          className="p-2 border mb-2"
          value={newTask.projectId}
          onChange={(e) =>
            setNewTask({ ...newTask, projectId: e.target.value })
          }
        />
        <input
          type="text"
          placeholder="Assigned to"
          className="p-2 border mb-2"
          value={newTask.assignedTo}
          onChange={(e) =>
            setNewTask({ ...newTask, assignedTo: e.target.value })
          }
        />
        <input
          type="text"
          placeholder="Description"
          className="p-2 border mb-2"
          value={newTask.description}
          onChange={(e) =>
            setNewTask({ ...newTask, description: e.target.value })
          }
        />
        <input
          type="number"
          placeholder="Expected Hours"
          className="p-2 border mb-2"
          value={newTask.expectedHours}
          onChange={(e) =>
            setNewTask({ ...newTask, expectedHours: e.target.value })
          }
        />
        <button
          onClick={() => handleAddTaskClick("todo")}
          className="bg-blue-500 text-white p-2 rounded mt-2 ml-2"
        >
          Add Task
        </button>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <div className="flex space-x-5 justify-center ml-14">
          {Object.entries(columns).map(([columnId, tasks]) => (
            <Column
              key={columnId}
              columnId={columnId}
              tasks={tasks}
              updateTask={updateTask}
              deleteTask={deleteTask}
            />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}

export default Board;
