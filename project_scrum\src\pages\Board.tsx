
import React, { useState } from "react";
import { DragDropContext, DropResult } from "@hello-pangea/dnd";
import { useLocation } from "react-router-dom";
import { useTask } from "../context/TaskContext";
import { Task } from "../types";
import FilterBar from "../components/shared/FilterBar";
import Column from "./Column";

function Board(): JSX.Element {
  const location = useLocation();
  const {
    getKanbanColumns,
    moveTask,
    createTask,
    updateTask,
    deleteTask,
    loading,
    error,
    getUniqueProjects,
    getUniqueAssignees
  } = useTask();

  // Get project filter from navigation state
  const projectFilter = location.state?.projectFilter;

  // Use backend data instead of local state
  const columns = getKanbanColumns();

  const [newTask, setNewTask] = useState<Omit<Task, "id" | "status">>({
    projectId: projectFilter || "",
    assignedTo: "",
    assignedTo2: "",
    description: "",
    expectedHours: 0,
    consumedHours: 0,
    zone: "",
  });

  const updateTaskLocal = async (taskId: string, updatedTask: Partial<Task>) => {
    try {
      await updateTask(taskId, updatedTask);
    } catch (error) {
      console.error('Failed to update task:', error);
      alert('Failed to update task. Please try again.');
    }
  };

  const deleteTaskLocal = async (taskId: string, columnId: string) => {
    try {
      await deleteTask(taskId);
    } catch (error) {
      console.error('Failed to delete task:', error);
      alert('Failed to delete task. Please try again.');
    }
  };

  const onDragEnd = async (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) return;

    const sourceColumnId = source.droppableId;
    const destColumnId = destination.droppableId;

    if (sourceColumnId === destColumnId && source.index === destination.index) {
      return; // No change
    }

    const sourceColumn = Array.from(columns[sourceColumnId]);
    const movedTask = sourceColumn[source.index];

    const getStatusLabel = (columnId: string) => {
      if (columnId === "todo") return "Backlog";
      if (columnId === "inProgress") return "Working";
      if (columnId === "check") return "Check";
      if (columnId === "done") return "Done";
      return columnId.charAt(0).toUpperCase() + columnId.slice(1);
    };

    const isBackwardMove = () => {
      const order = ["todo", "inProgress", "check", "done"];
      return order.indexOf(destColumnId) < order.indexOf(sourceColumnId);
    };

    const newStatus = isBackwardMove()
      ? `Re ${getStatusLabel(destColumnId)}`
      : getStatusLabel(destColumnId);

    try {
      await moveTask(movedTask.id, newStatus);
    } catch (error) {
      console.error('Failed to move task:', error);
      alert('Failed to move task. Please try again.');
    }
  };
  

  const handleAddTaskClick = async (columnId: string) => {
    if (
      !newTask.assignedTo ||
      !newTask.description ||
      !newTask.expectedHours ||
      !newTask.projectId
    ) {
      alert("Please fill all required fields");
      return;
    }

    const status =
      columnId === "todo"
        ? "Backlog"
        : columnId === "inProgress"
        ? "Working"
        : columnId === "check"
        ? "Check"
        : "Done";

    const taskData: Omit<Task, "id"> = {
      assignedTo: newTask.assignedTo,
      assignedTo2: newTask.assignedTo2,
      description: newTask.description,
      expectedHours: newTask.expectedHours,
      consumedHours: newTask.consumedHours,
      projectId: newTask.projectId,
      zone: newTask.zone,
      status,
    };

    try {
      await createTask(taskData);
      setNewTask({
        projectId: projectFilter || "",
        assignedTo: "",
        assignedTo2: "",
        description: "",
        expectedHours: 0,
        consumedHours: 0,
        zone: "",
      });
    } catch (error) {
      console.error('Failed to create task:', error);
      alert('Failed to create task. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="text-lg">Loading tasks...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col p-6">
      <h1 className="text-2xl font-bold mb-6">
        Kanban Board {projectFilter && `- ${projectFilter}`}
      </h1>

      {/* Filter Bar */}
      <FilterBar className="mb-6" />

      {/* Add Task Form */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg w-full max-w-4xl">
        <h2 className="text-lg font-semibold mb-4">Add New Task</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <select
            className="p-2 border rounded"
            value={newTask.projectId}
            onChange={(e) => setNewTask({ ...newTask, projectId: e.target.value })}
          >
            <option value="">Select Project</option>
            {getUniqueProjects().map(project => (
              <option key={project} value={project}>{project}</option>
            ))}
          </select>

          <select
            className="p-2 border rounded"
            value={newTask.assignedTo}
            onChange={(e) => setNewTask({ ...newTask, assignedTo: e.target.value })}
          >
            <option value="">Assign To</option>
            {getUniqueAssignees().map(assignee => (
              <option key={assignee} value={assignee}>{assignee}</option>
            ))}
          </select>

          <input
            type="text"
            placeholder="Zone"
            className="p-2 border rounded"
            value={newTask.zone}
            onChange={(e) => setNewTask({ ...newTask, zone: e.target.value })}
          />

          <input
            type="number"
            placeholder="Expected Hours"
            className="p-2 border rounded"
            value={newTask.expectedHours}
            onChange={(e) => setNewTask({ ...newTask, expectedHours: Number(e.target.value) })}
          />

          <input
            type="text"
            placeholder="Description"
            className="p-2 border rounded col-span-2"
            value={newTask.description}
            onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
          />
        </div>

        <button
          onClick={() => handleAddTaskClick("todo")}
          className="bg-blue-500 text-white px-4 py-2 rounded mt-4 hover:bg-blue-600"
        >
          Add Task to Backlog
        </button>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <div className="flex space-x-5 justify-center w-full overflow-x-auto">
          {Object.entries(columns).map(([columnId, tasks]) => (
            <Column
              key={columnId}
              columnId={columnId}
              tasks={tasks}
              updateTask={updateTaskLocal}
              deleteTask={deleteTaskLocal}
            />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}

export default Board;
