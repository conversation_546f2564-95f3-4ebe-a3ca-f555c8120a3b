import React from 'react';
import { useTask } from '../../context/TaskContext';
import { Filter, X } from 'lucide-react';

interface FilterBarProps {
  showProjectFilter?: boolean;
  showZoneFilter?: boolean;
  className?: string;
}

const FilterBar: React.FC<FilterBarProps> = ({ 
  showProjectFilter = true, 
  showZoneFilter = true,
  className = ""
}) => {
  const {
    selectedProject,
    selectedZone,
    setSelectedProject,
    setSelectedZone,
    getUniqueProjects,
    getUniqueZones,
    getTaskStats
  } = useTask();

  const stats = getTaskStats();

  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Filter className="h-5 w-5 mr-2 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Filters</h3>
        </div>
        <div className="text-sm text-gray-600">
          {stats.total} total tasks
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {showProjectFilter && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project
            </label>
            <select
              value={selectedProject || ''}
              onChange={(e) => setSelectedProject(e.target.value || null)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Projects</option>
              {getUniqueProjects().map(project => (
                <option key={project} value={project}>
                  {project} ({stats.byProject[project] || 0} tasks)
                </option>
              ))}
            </select>
          </div>
        )}

        {showZoneFilter && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Zone
            </label>
            <select
              value={selectedZone || ''}
              onChange={(e) => setSelectedZone(e.target.value || null)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Zones</option>
              {getUniqueZones().map(zone => (
                <option key={zone} value={zone}>
                  {zone} ({stats.byZone[zone] || 0} tasks)
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Active Filters Display */}
      {(selectedProject || selectedZone) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Active Filters:</span>
            <button
              onClick={() => {
                setSelectedProject(null);
                setSelectedZone(null);
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Clear All
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedProject && (
              <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                Project: {selectedProject}
                <button
                  onClick={() => setSelectedProject(null)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {selectedZone && (
              <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                Zone: {selectedZone}
                <button
                  onClick={() => setSelectedZone(null)}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterBar;
