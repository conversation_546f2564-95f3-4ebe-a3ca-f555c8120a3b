import React from 'react';
import { useProject } from '../context/ProjectContext';
import { useNavigate } from 'react-router-dom';
import { Plus, Users, Clock, BarChart3, CheckCircle } from 'lucide-react';

const Projects = () => {
  const { projects, loading, error } = useProject();
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg">Loading projects...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
          <p className="text-gray-600 mt-1">Backend projects from task data</p>
        </div>
        <div className="text-sm text-gray-600">
          {projects.length} projects found
        </div>
      </div>

      {projects.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">No projects found in backend data</div>
          <p className="text-sm text-gray-400">Projects are automatically created from task data</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map(project => (
            <div
              key={project.id}
              className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-gray-200"
              onClick={() => navigate(`/projects/${project.id}`)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">{project.name}</h3>
                  <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {project.taskCount} tasks
                  </div>
                </div>

                <div className="space-y-3">
                  {/* Progress Bar */}
                  <div>
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{Math.round(project.progress)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(project.progress, 100)}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Users className="w-4 h-4 mr-1" />
                      <span>{project.team.length} members</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Clock className="w-4 h-4 mr-1" />
                      <span>{project.totalExpectedHours}h planned</span>
                    </div>
                  </div>

                  {/* Zones */}
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Zones:</div>
                    <div className="flex flex-wrap gap-1">
                      {project.zones.slice(0, 3).map(zone => (
                        <span key={zone} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                          {zone}
                        </span>
                      ))}
                      {project.zones.length > 3 && (
                        <span className="text-xs text-gray-500">+{project.zones.length - 3} more</span>
                      )}
                    </div>
                  </div>

                  {/* Status Summary */}
                  <div className="flex justify-between text-xs text-gray-500 pt-2 border-t">
                    <span>✅ {project.statusCounts['Done'] || 0} done</span>
                    <span>🔄 {project.statusCounts['Working'] || 0} working</span>
                    <span>📋 {project.statusCounts['Backlog'] || 0} backlog</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Projects;