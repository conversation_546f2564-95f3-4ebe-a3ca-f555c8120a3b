import { createContext, useContext, useState, ReactNode } from 'react';
import { Project, Story, Sprint } from '../types';
import { projects as initialProjects, stories as initialStories, sprints as initialSprints } from '../data/mockData';

interface ProjectContextType {
  projects: Project[];
  stories: Story[];
  sprints: Sprint[];
  currentProject: Project | null;
  setCurrentProject: (project: Project | null) => void;
  
  // Project methods
  addProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProject: (project: Project) => void;
  deleteProject: (projectId: string) => void;
  
  // Story methods
  addStory: (story: Omit<Story, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateStory: (story: Story) => void;
  deleteStory: (storyId: string) => void;
  moveStory: (storyId: string, newStatus: Story['status'], sprintId?: string) => void;
  
  // Sprint methods
  addSprint: (sprint: Omit<Sprint, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateSprint: (sprint: Sprint) => void;
  deleteSprint: (sprintId: string) => void;
  startSprint: (sprintId: string) => void;
  endSprint: (sprintId: string) => void;
  
  // Helper methods
  getProjectStories: (projectId: string) => Story[];
  getSprintStories: (sprintId: string) => Story[];
  getBacklogStories: (projectId: string) => Story[];
  getProjectSprints: (projectId: string) => Sprint[];
  getActiveSprint: (projectId: string) => Sprint | undefined;
  getProjectById: (projectId: string) => Project | undefined;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider = ({ children }: { children: ReactNode }) => {
  const [projects, setProjects] = useState<Project[]>(initialProjects);
  const [stories, setStories] = useState<Story[]>(initialStories);
  const [sprints, setSprints] = useState<Sprint[]>(initialSprints);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);

  // Project methods
  const addProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProject: Project = {
      ...projectData,
      id: `project-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setProjects(prevProjects => [...prevProjects, newProject]);
    setCurrentProject(newProject);
  };

  const updateProject = (updatedProject: Project) => {
    const newProjects = projects.map(p => 
      p.id === updatedProject.id ? { ...updatedProject, updatedAt: new Date() } : p
    );
    setProjects(newProjects);
    
    if (currentProject?.id === updatedProject.id) {
      setCurrentProject({ ...updatedProject, updatedAt: new Date() });
    }
  };

  const deleteProject = (projectId: string) => {
    setProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));
    if (currentProject?.id === projectId) {
      setCurrentProject(null);
    }
    
    setSprints(prevSprints => prevSprints.filter(s => s.projectId !== projectId));
    setStories(prevStories => prevStories.filter(s => s.projectId !== projectId));
  };

  // Story methods
  const addStory = (storyData: Omit<Story, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newStory: Story = {
      ...storyData,
      id: `story-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setStories(prevStories => [...prevStories, newStory]);
  };

  const updateStory = (updatedStory: Story) => {
    setStories(prevStories => 
      prevStories.map(s => 
        s.id === updatedStory.id ? { ...updatedStory, updatedAt: new Date() } : s
      )
    );
  };

  const deleteStory = (storyId: string) => {
    setStories(prevStories => prevStories.filter(s => s.id !== storyId));
  };

  const moveStory = (storyId: string, newStatus: Story['status'], sprintId?: string) => {
    setStories(prevStories => 
      prevStories.map(s => 
        s.id === storyId 
          ? { 
              ...s, 
              status: newStatus, 
              sprintId: sprintId || s.sprintId,
              updatedAt: new Date() 
            } 
          : s
      )
    );
  };

  // Sprint methods
  const addSprint = (sprintData: Omit<Sprint, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newSprint: Sprint = {
      ...sprintData,
      id: `sprint-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setSprints(prevSprints => [...prevSprints, newSprint]);
  };

  const updateSprint = (updatedSprint: Sprint) => {
    setSprints(prevSprints => 
      prevSprints.map(s => 
        s.id === updatedSprint.id ? { ...updatedSprint, updatedAt: new Date() } : s
      )
    );
  };

  const deleteSprint = (sprintId: string) => {
    setSprints(prevSprints => prevSprints.filter(s => s.id !== sprintId));
    
    setStories(prevStories => 
      prevStories.map(s => 
        s.sprintId === sprintId 
          ? { ...s, sprintId: undefined, status: 'backlog' as const, updatedAt: new Date() } 
          : s
      )
    );
  };

  const startSprint = (sprintId: string) => {
    const sprintToStart = sprints.find(s => s.id === sprintId);
    if (!sprintToStart) return;
    
    setSprints(prevSprints => 
      prevSprints.map(s => 
        s.projectId === sprintToStart.projectId 
          ? { ...s, isActive: s.id === sprintId, updatedAt: new Date() }
          : s
      )
    );
  };

  const endSprint = (sprintId: string) => {
    setSprints(prevSprints => 
      prevSprints.map(s => 
        s.id === sprintId ? { ...s, isActive: false, updatedAt: new Date() } : s
      )
    );
  };

  // Helper methods
  const getProjectStories = (projectId: string) => {
    return stories.filter(s => s.projectId === projectId);
  };

  const getSprintStories = (sprintId: string) => {
    return stories.filter(s => s.sprintId === sprintId);
  };

  const getBacklogStories = (projectId: string) => {
    return stories.filter(s => s.projectId === projectId && !s.sprintId);
  };

  const getProjectSprints = (projectId: string) => {
    return sprints.filter(s => s.projectId === projectId);
  };

  const getActiveSprint = (projectId: string) => {
    return sprints.find(s => s.projectId === projectId && s.isActive);
  };

  const getProjectById = (projectId: string) => {
    return projects.find(p => p.id === projectId);
  };

  return (
    <ProjectContext.Provider 
      value={{
        projects,
        stories,
        sprints,
        currentProject,
        setCurrentProject,
        
        // Project methods
        addProject,
        updateProject,
        deleteProject,
        
        // Story methods
        addStory,
        updateStory,
        deleteStory,
        moveStory,
        
        // Sprint methods
        addSprint,
        updateSprint,
        deleteSprint,
        startSprint,
        endSprint,
        
        // Helper methods
        getProjectStories,
        getSprintStories,
        getBacklogStories,
        getProjectSprints,
        getActiveSprint,
        getProjectById,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};