import { User, UserRole, Project, Story, Sprint, Status } from '../types';

// Mock Users
export const users: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.L1,
    avatar: 'blank.jpg',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON> ',
    email: '<EMAIL>',
    role: UserRole.IZ,
    avatar: 'kusum_dci.jpg',
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: UserRole.HW,
    avatar: 'blank.jpg',
  },
  {
    id: '4',
    name: '<PERSON><PERSON> ',
    email: '<EMAIL>',
    role: UserRole.HW,
    avatar: 'rajiv_dci.jpg',
  },
  {
    id: '5',
    name: '<PERSON><PERSON><PERSON> ',
    email: '<EMAIL>',
    role: UserRole.IZ,
    avatar: 'blank.jpg',
  },
];

// Mock Projects
export const projects: Project[] = [
  {
    id: '1',
    name: 'P004976-BMM BF',
    description: 'BMM BF',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '2',
    name: 'P005377-TSJ BFH & I-4th Stove',
    description: 'TSJ BFH & I-4th Stove',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '3',
    name: 'P005766-AMNS BF2&BF3',
    description: 'AMNS BF2 & BF3 & GAD',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '4',
    name: 'P005960-JSW Dolvi-BF3 Stoves',
    description: 'JSW Dolvi-BF3 Stoves',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '5',
    name: 'P006006-AMNS Sub lance',
    description: 'AMNS Sub lance',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '6',
    name: 'P006049-TSJ LD1 Sub lance',
    description: 'TSJ LD1 Sub lance (HOLD)',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '7',
    name: 'P006198-DSP BF3-BF ',
    description: 'DSP BF3-BF Upgradation',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '8',
    name: 'P006205-JSOL Sub lance',
    description: 'JSOL Sub lance',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '9',
    name: 'P006273-TSM BF2-4th Stove',
    description: 'TSM BF2-4th Stove',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '10',
    name: 'P006384-DSP BF3-Stove',
    description: 'DSP BF3-Stove upgradation',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '11',
    name: 'P006416-JSW Sub lance -3',
    description: 'JSW Sub lance -3',
    team: ['1', '2', '3', '4', '5'],
    vision: '',
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '12',
    name: 'P00XXX-JSL HMDS',
    description: 'JSL HMDS',
    team: ['1', '2', '4'],
    vision: '',
    createdAt: new Date('2023-03-22'),
    updatedAt: new Date('2023-06-10'),
  },
];

// Mock Sprints
export const sprints: Sprint[] = [
  {
    id: '1',
    name: 'Sprint 1: User Authentication',
    projectId: '1',
    startDate: new Date('2023-06-01'),
    endDate: new Date('2023-06-14'),
    goals: 'Implement user authentication and basic profile management',
    isActive: false,
    createdAt: new Date('2023-05-25'),
    updatedAt: new Date('2023-06-14'),
  },
  {
    id: '2',
    name: 'Sprint 2: Product Catalog',
    projectId: '1',
    startDate: new Date('2023-06-15'),
    endDate: new Date('2023-06-28'),
    goals: 'Build product listing and detail pages',
    isActive: true,
    createdAt: new Date('2023-06-10'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: '3',
    name: 'Sprint 1: Dashboard',
    projectId: '2',
    startDate: new Date('2023-06-05'),
    endDate: new Date('2023-06-18'),
    goals: 'Create main dashboard and navigation',
    isActive: true,
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2023-06-05'),
  },
];

// Mock Stories/Tasks
export const stories: Story[] = [
  // E-Commerce Project - Sprint 1
  {
    id: '1',
    title: 'Implement user registration',
    description: 'Create registration form with email validation',
    status: Status.DONE,
    assigneeId: '4',
    sprintId: '1',
    projectId: '1',
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2023-06-10'),
  },
  {
    id: '2',
    title: 'Implement user login',
    description: 'Create login form with authentication',
    status: Status.DONE,
    assigneeId: '4',
    sprintId: '1',
    projectId: '1',
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2023-06-12'),
  },
  {
    id: '3',
    title: 'Create user profile page',
    description: 'Design and implement user profile page',
    status: Status.DONE,
    assigneeId: '5',
    sprintId: '1',
    projectId: '1',
    createdAt: new Date('2023-06-02'),
    updatedAt: new Date('2023-06-14'),
  },

  // E-Commerce Project - Sprint 2
  {
    id: '4',
    title: 'Design product card component',
    description: 'Create reusable product card with image, title, price',
    status: Status.DONE,
    assigneeId: '5',
    sprintId: '2',
    projectId: '1',
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2023-06-17'),
  },
  {
    id: '5',
    title: 'Implement product catalog page',
    description: 'Create page to display all products with filtering',
    status: Status.CHECK,
    assigneeId: '4',
    sprintId: '2',
    projectId: '1',
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2023-06-18'),
  },
  {
    id: '6',
    title: 'Create product detail page',
    description: 'Design and implement product detail view',
    status: Status.WORKING,
    assigneeId: '5',
    sprintId: '2',
    projectId: '1',
    createdAt: new Date('2023-06-16'),
    updatedAt: new Date('2023-06-16'),
  },
  {
    id: '7',
    title: 'Implement product search',
    description: 'Add search functionality to the catalog',
    status: Status.DONE,
    assigneeId: '4',
    sprintId: '2',
    projectId: '1',
    createdAt: new Date('2023-06-16'),
    updatedAt: new Date('2023-06-16'),
  },

  // E-Commerce Project - Backlog
  {
    id: '8',
    title: 'Shopping cart functionality',
    description: 'Implement adding/removing products from cart',
    status: Status.BACKLOG,
    assigneeId: '2',
    projectId: '1',
    createdAt: new Date('2023-05-20'),
    updatedAt: new Date('2023-05-20'),
  },
  {
    id: '9',
    title: 'Checkout process',
    description: 'Implement multi-step checkout with payment',
    status: Status.BACKLOG,
    assigneeId: '3',
    projectId: '1',
    createdAt: new Date('2023-05-20'),
    updatedAt: new Date('2023-05-20'),
  },

  // CRM Project - Sprint 1
  {
    id: '10',
    title: 'Design dashboard layout',
    description: 'Create responsive dashboard layout with navigation',
    status: Status.DONE,
    assigneeId: '4',
    sprintId: '3',
    projectId: '2',
    createdAt: new Date('2023-06-05'),
    updatedAt: new Date('2023-06-10'),
  },
  {
    id: '11',
    title: 'Create widgets for dashboard',
    description: 'Implement various dashboard widgets',
    status: Status.CHECK,
    assigneeId: '4',
    sprintId: '3',
    projectId: '2',
    createdAt: new Date('2023-06-05'),
    updatedAt: new Date('2023-06-15'),
  },
];

// Active user (simulating logged in user)
export const currentUser: User = users[0]; // Admin user
