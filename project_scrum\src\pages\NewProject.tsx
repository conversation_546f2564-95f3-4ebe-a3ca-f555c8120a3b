import React, { useState } from 'react';
import { useProject } from '../context/ProjectContext';
import { useNavigate } from 'react-router-dom';
import { users } from '../data/mockData';
import Button from '../components/ui/Button';
import { AlertCircle } from 'lucide-react';

const NewProject = () => {
  const navigate = useNavigate();
  const { addProject } = useProject();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    vision: '',
    team: [] as string[]
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    if (!formData.name.trim()) {
      setError('Project name is required');
      return;
    }

    if (!formData.description.trim()) {
      setError('Project description is required');
      return;
    }

    if (formData.team.length === 0) {
      setError('Please select at least one team member');
      return;
    }

    setIsSubmitting(true);
    
    try {
      await addProject(formData);
      navigate('/dashboard');
    } catch (error) {
      setError('Failed to create project. Please try again.');
      console.error('Failed to create project:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Create New Project</h1>
      
      <form onSubmit={handleSubmit} className="max-w-2xl bg-white rounded-lg shadow-sm p-6">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        )}

        <div className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Project Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows={3}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>

          <div>
            <label htmlFor="vision" className="block text-sm font-medium text-gray-700">
              Vision (Optional)
            </label>
            <textarea
              id="vision"
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={formData.vision}
              onChange={(e) => setFormData({ ...formData, vision: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Team Members <span className="text-red-500">*</span>
            </label>
            <div className="space-y-2 max-h-60 overflow-y-auto p-4 border border-gray-200 rounded-md">
              {users.map(user => (
                <label key={user.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    checked={formData.team.includes(user.id)}
                    onChange={(e) => {
                      const newTeam = e.target.checked
                        ? [...formData.team, user.id]
                        : formData.team.filter(id => id !== user.id);
                      setFormData({ ...formData, team: newTeam });
                    }}
                  />
                  <span className="ml-2 text-sm text-gray-900">{user.name}</span>
                  <span className="ml-2 text-xs text-gray-500 capitalize">({user.role})</span>
                </label>
              ))}
            </div>
            {formData.team.length === 0 && (
              <p className="mt-1 text-xs text-red-500">Please select at least one team member</p>
            )}
          </div>
        </div>

        <div className="mt-6 flex items-center justify-end space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/projects')}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
            disabled={isSubmitting || formData.team.length === 0}
          >
            Create Project
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewProject;