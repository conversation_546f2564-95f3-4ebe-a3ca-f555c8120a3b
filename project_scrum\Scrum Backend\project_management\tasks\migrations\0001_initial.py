# Generated by Django 5.0.13 on 2025-05-06 12:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Task',
            fields=[
                ('s_no', models.IntegerField(primary_key=True, serialize=False)),
                ('project_zone_no_zone', models.<PERSON>r<PERSON>ield(max_length=100)),
                ('active_list', models.CharField(blank=True, max_length=100, null=True)),
                ('job_description', models.TextField(blank=True, null=True)),
                ('expected_hours', models.IntegerField(blank=True, null=True)),
                ('consumed_hours', models.IntegerField(blank=True, null=True)),
                ('zone', models.Char<PERSON>ield(blank=True, max_length=10, null=True)),
                ('responsible_1', models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('responsible_2', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('job_status', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50, null=True)),
            ],
            options={
                'db_table': 'job_tracking',
                'managed': False,
            },
        ),
    ]
