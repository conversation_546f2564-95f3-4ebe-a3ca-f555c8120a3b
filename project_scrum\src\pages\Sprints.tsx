import React from 'react';
import { useProject } from '../context/ProjectContext';
import { Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const Sprints = () => {
  const { currentProject, getProjectSprints } = useProject();
  const navigate = useNavigate();
  const sprints = currentProject ? getProjectSprints(currentProject.id) : [];

  if (!currentProject) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Please select a project to view its sprints.</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Sprints</h1>
        <button
          onClick={() => navigate('/sprints/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
        >
          <Plus className="w-5 h-5 mr-2" />
          New Sprint
        </button>
      </div>

      <div className="space-y-4">
        {sprints.map(sprint => (
          <div key={sprint.id} className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{sprint.name}</h3>
              <span className={`px-2 py-1 rounded-full text-sm ${
                sprint.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {sprint.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Goals:</strong> {sprint.goals || 'No goals specified'}</p>
              <p>
                <strong>Duration:</strong> {' '}
                {new Date(sprint.startDate).toLocaleDateString()} - {' '}
                {new Date(sprint.endDate).toLocaleDateString()}
              </p>
            </div>
            
            <div className="mt-4 flex justify-end space-x-4">
              <button className="text-blue-600 hover:text-blue-800">
                View Details
              </button>
              {!sprint.isActive && (
                <button className="text-green-600 hover:text-green-800">
                  Start Sprint
                </button>
              )}
            </div>
          </div>
        ))}

        {sprints.length === 0 && (
          <div className="text-center py-12 bg-white rounded-lg">
            <p className="text-gray-600">No sprints created yet.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sprints;