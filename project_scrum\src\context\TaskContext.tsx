import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Task, BackendTask } from '../types';
import { useTaskData } from '../hooks/useTaskData';

interface TaskContextType {
  // Task data
  tasks: Task[];
  loading: boolean;
  error: string | null;
  
  // Task operations
  createTask: (taskData: Omit<Task, 'id'>) => Promise<void>;
  updateTask: (id: string, taskData: Partial<Task>) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
  refetch: () => Promise<void>;
  
  // Task filtering and organization
  getTasksByStatus: (status: string) => Task[];
  getTasksByZone: (zone: string) => Task[];
  getTasksByProject: (projectId: string) => Task[];
  getTasksByAssignee: (assignee: string) => Task[];
  
  // Kanban board operations
  moveTask: (taskId: string, newStatus: string) => Promise<void>;
  getKanbanColumns: () => { [key: string]: Task[] };
  
  // Statistics and aggregations
  getTaskStats: () => {
    total: number;
    byStatus: Record<string, number>;
    byZone: Record<string, number>;
    byProject: Record<string, number>;
  };
  
  // Project and zone data derived from tasks
  getUniqueProjects: () => string[];
  getUniqueZones: () => string[];
  getUniqueAssignees: () => string[];
  
  // Current selections for filtering
  selectedProject: string | null;
  selectedZone: string | null;
  setSelectedProject: (projectId: string | null) => void;
  setSelectedZone: (zone: string | null) => void;
}

const TaskContext = createContext<TaskContextType | undefined>(undefined);

export const TaskProvider = ({ children }: { children: ReactNode }) => {
  const {
    tasks,
    loading,
    error,
    createTask: createTaskAPI,
    updateTask: updateTaskAPI,
    deleteTask: deleteTaskAPI,
    refetch,
    getTasksByZone: getTasksByZoneAPI,
    getTasksByStatus: getTasksByStatusAPI,
  } = useTaskData();

  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedZone, setSelectedZone] = useState<string | null>(null);

  // Task filtering functions
  const getTasksByStatus = (status: string): Task[] => {
    return tasks.filter(task => task.status === status);
  };

  const getTasksByZone = (zone: string): Task[] => {
    return tasks.filter(task => task.zone === zone);
  };

  const getTasksByProject = (projectId: string): Task[] => {
    return tasks.filter(task => task.projectId === projectId);
  };

  const getTasksByAssignee = (assignee: string): Task[] => {
    return tasks.filter(task => 
      task.assignedTo === assignee || task.assignedTo2 === assignee
    );
  };

  // Kanban board operations
  const moveTask = async (taskId: string, newStatus: string): Promise<void> => {
    await updateTaskAPI(taskId, { status: newStatus });
  };

  const getKanbanColumns = (): { [key: string]: Task[] } => {
    const columns = {
      todo: getTasksByStatus('Backlog'),
      inProgress: getTasksByStatus('Working'),
      check: getTasksByStatus('Check'),
      done: getTasksByStatus('Done'),
    };
    
    // Apply filters if selected
    if (selectedProject) {
      Object.keys(columns).forEach(key => {
        columns[key as keyof typeof columns] = columns[key as keyof typeof columns]
          .filter(task => task.projectId === selectedProject);
      });
    }
    
    if (selectedZone) {
      Object.keys(columns).forEach(key => {
        columns[key as keyof typeof columns] = columns[key as keyof typeof columns]
          .filter(task => task.zone === selectedZone);
      });
    }
    
    return columns;
  };

  // Statistics and aggregations
  const getTaskStats = () => {
    const byStatus: Record<string, number> = {};
    const byZone: Record<string, number> = {};
    const byProject: Record<string, number> = {};

    tasks.forEach(task => {
      // Status stats
      const status = task.status || 'Unknown';
      byStatus[status] = (byStatus[status] || 0) + 1;

      // Zone stats
      const zone = task.zone || 'Unknown';
      byZone[zone] = (byZone[zone] || 0) + 1;

      // Project stats
      const project = task.projectId || 'Unknown';
      byProject[project] = (byProject[project] || 0) + 1;
    });

    return {
      total: tasks.length,
      byStatus,
      byZone,
      byProject,
    };
  };

  // Get unique values for dropdowns and filters
  const getUniqueProjects = (): string[] => {
    return Array.from(new Set(tasks.map(task => task.projectId).filter(Boolean)));
  };

  const getUniqueZones = (): string[] => {
    return Array.from(new Set(tasks.map(task => task.zone).filter(Boolean)));
  };

  const getUniqueAssignees = (): string[] => {
    const assignees = new Set<string>();
    tasks.forEach(task => {
      if (task.assignedTo) assignees.add(task.assignedTo);
      if (task.assignedTo2) assignees.add(task.assignedTo2);
    });
    return Array.from(assignees);
  };

  // Task operations with error handling
  const createTask = async (taskData: Omit<Task, 'id'>): Promise<void> => {
    try {
      await createTaskAPI(taskData);
    } catch (error) {
      console.error('Failed to create task:', error);
      throw error;
    }
  };

  const updateTask = async (id: string, taskData: Partial<Task>): Promise<void> => {
    try {
      await updateTaskAPI(id, taskData);
    } catch (error) {
      console.error('Failed to update task:', error);
      throw error;
    }
  };

  const deleteTask = async (id: string): Promise<void> => {
    try {
      await deleteTaskAPI(id);
    } catch (error) {
      console.error('Failed to delete task:', error);
      throw error;
    }
  };

  const contextValue: TaskContextType = {
    // Task data
    tasks,
    loading,
    error,
    
    // Task operations
    createTask,
    updateTask,
    deleteTask,
    refetch,
    
    // Task filtering and organization
    getTasksByStatus,
    getTasksByZone,
    getTasksByProject,
    getTasksByAssignee,
    
    // Kanban board operations
    moveTask,
    getKanbanColumns,
    
    // Statistics and aggregations
    getTaskStats,
    
    // Project and zone data derived from tasks
    getUniqueProjects,
    getUniqueZones,
    getUniqueAssignees,
    
    // Current selections for filtering
    selectedProject,
    selectedZone,
    setSelectedProject,
    setSelectedZone,
  };

  return (
    <TaskContext.Provider value={contextValue}>
      {children}
    </TaskContext.Provider>
  );
};

export const useTask = () => {
  const context = useContext(TaskContext);
  if (context === undefined) {
    throw new Error('useTask must be used within a TaskProvider');
  }
  return context;
};
