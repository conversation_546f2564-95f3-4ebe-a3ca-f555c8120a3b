import React, { useState } from 'react';
import { Story, Status } from '../../types';

interface PieChartProps {
  stories: Story[];
}

const PieChart: React.FC<PieChartProps> = ({ stories }) => {
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);
  const total = stories.length;
  
  // Calculate counts
  const backlogCount = stories.filter(s => s.status === Status.BACKLOG).length;
  const workingCount = stories.filter(s => s.status === Status.WORKING).length;
  const checkCount = stories.filter(s => s.status === Status.CHECK).length;
  const doneCount = stories.filter(s => s.status === Status.DONE).length;
  
  // Calculate percentages
  const calculatePercentage = (count: number) => {
    return total === 0 ? 0 : Math.round((count / total) * 100);
  };
  
  const backlogPercentage = calculatePercentage(backlogCount);
  const workingPercentage = calculatePercentage(workingCount);
  const checkPercentage = calculatePercentage(checkCount);
  const donePercentage = calculatePercentage(doneCount);
  
  // Calculate stroke dasharray and offset for each segment
  const radius = 40;
  const circumference = 2 * Math.PI * radius;
  
  const backlogDash = (backlogPercentage / 100) * circumference;
  const workingDash = (workingPercentage / 100) * circumference;
  const checkDash = (checkPercentage / 100) * circumference;
  const doneDash = (donePercentage / 100) * circumference;
  
  const workingOffset = backlogDash;
  const checkOffset = workingOffset + workingDash;
  const doneOffset = checkOffset + checkDash;

  return (
    <div className="p-6 bg-white shadow-sm rounded-lg">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Project Status</h3>
      
      <div className="flex items-center justify-center mb-6">
        <div className="relative w-64 h-64">
          <svg className="w-full h-full transform -rotate-90">
            {/* Background circle */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              stroke="#E5E7EB"
              strokeWidth="20"
              fill="none"
            />
            
            {/* Backlog segment */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              stroke="#94A3B8"
              strokeWidth="20"
              fill="none"
              strokeDasharray={`${backlogDash} ${circumference}`}
              onMouseEnter={() => setHoveredSegment('backlog')}
              onMouseLeave={() => setHoveredSegment(null)}
              className="cursor-pointer transition-all duration-200 hover:opacity-80"
            />
            
            {/* Working segment */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              stroke="#93C5FD"
              strokeWidth="20"
              fill="none"
              strokeDasharray={`${workingDash} ${circumference}`}
              strokeDashoffset={-workingOffset}
              onMouseEnter={() => setHoveredSegment('working')}
              onMouseLeave={() => setHoveredSegment(null)}
              className="cursor-pointer transition-all duration-200 hover:opacity-80"
            />
            
            {/* Check segment */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              stroke="#FDE68A"
              strokeWidth="20"
              fill="none"
              strokeDasharray={`${checkDash} ${circumference}`}
              strokeDashoffset={-checkOffset}
              onMouseEnter={() => setHoveredSegment('check')}
              onMouseLeave={() => setHoveredSegment(null)}
              className="cursor-pointer transition-all duration-200 hover:opacity-80"
            />
            
            {/* Done segment */}
            <circle
              cx="50%"
              cy="50%"
              r={radius}
              stroke="#86EFAC"
              strokeWidth="20"
              fill="none"
              strokeDasharray={`${doneDash} ${circumference}`}
              strokeDashoffset={-doneOffset}
              onMouseEnter={() => setHoveredSegment('done')}
              onMouseLeave={() => setHoveredSegment(null)}
              className="cursor-pointer transition-all duration-200 hover:opacity-80"
            />
          </svg>
          
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span className="text-3xl font-bold text-gray-900">{total}</span>
            <span className="text-sm text-gray-500">Total Stories</span>
            {hoveredSegment && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm whitespace-nowrap">
                {hoveredSegment === 'backlog' && `Backlog: ${backlogPercentage}%`}
                {hoveredSegment === 'working' && `Working: ${workingPercentage}%`}
                {hoveredSegment === 'check' && `Check: ${checkPercentage}%`}
                {hoveredSegment === 'done' && `Done: ${donePercentage}%`}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center p-3 bg-gray-50 rounded-lg">
          <div className="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
          <div>
            <div className="text-sm font-medium text-gray-900">{backlogPercentage}% Backlog</div>
            <div className="text-xs text-gray-500">{backlogCount} stories</div>
          </div>
        </div>
        
        <div className="flex items-center p-3 bg-blue-50 rounded-lg">
          <div className="w-3 h-3 bg-blue-300 rounded-full mr-2"></div>
          <div>
            <div className="text-sm font-medium text-blue-900">{workingPercentage}% Working</div>
            <div className="text-xs text-blue-500">{workingCount} stories</div>
          </div>
        </div>
        
        <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
          <div className="w-3 h-3 bg-yellow-200 rounded-full mr-2"></div>
          <div>
            <div className="text-sm font-medium text-yellow-900">{checkPercentage}% Check</div>
            <div className="text-xs text-yellow-500">{checkCount} stories</div>
          </div>
        </div>
        
        <div className="flex items-center p-3 bg-green-50 rounded-lg">
          <div className="w-3 h-3 bg-green-200 rounded-full mr-2"></div>
          <div>
            <div className="text-sm font-medium text-green-900">{donePercentage}% Done</div>
            <div className="text-xs text-green-500">{doneCount} stories</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PieChart;