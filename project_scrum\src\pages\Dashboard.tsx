import React, { useState, useEffect } from 'react';
import KanbanBoard from '../components/dashboard/KanbanBoard';
//import PieC<PERSON> from '../components/dashboard/PieChart';
import TeamOverview from '../components/dashboard/TeamOverview';
import { useProject } from '../context/ProjectContext';
import { Status } from '../types';
import { users } from '../data/mockData';
import Bar<PERSON>hart from '../components/dashboard/BarChart';

const Dashboard: React.FC = () => {
  const { 
    currentProject, 
    projects, 
    getProjectStories, 
    getActiveSprint, 
    moveStory, 
    setCurrentProject 
  } = useProject();
  
  const [activeProject, setActiveProject] = useState(currentProject || projects[0]);
  
  // Set a default project if none is selected
  useEffect(() => {
    if (!currentProject && projects.length > 0) {
      setCurrentProject(projects[0]);
    }
  }, [currentProject, projects, setCurrentProject]);
  
  useEffect(() => {
    if (currentProject) {
      setActiveProject(currentProject);
    }
  }, [currentProject]);
  
  // If no projects exist
  if (!activeProject) {
    return (
      <div className="min-h-screen bg-gray-100 p-6 flex flex-col items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md text-center max-w-md w-full">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Welcome to ScrumMaster!</h2>
          <p className="text-gray-600 mb-6">
            Get started by creating your first project.
          </p>
          <a 
            href="/projects/new" 
            className="inline-block bg-blue-600 text-white font-medium px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Create First Project
          </a>
        </div>
      </div>
    );
  }
  
  const stories = getProjectStories(activeProject.id);
  const teamMembers = users.filter(user => activeProject.team.includes(user.id));
  
  const handleStatusChange = (storyId: string, newStatus: Status) => {
    moveStory(storyId, newStatus);
  };
  
  return (
    <div className="p-4 md:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{activeProject.name} Dashboard</h1>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main content - 2/3 width on large screens */}
        <div className="lg:col-span-2 space-y-6">
          {/* Project Status */}
          {/* <PieChart stories={stories} /> */}
          <BarChart stories = {stories}/>
          
          {/* Kanban board */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Project Board</h2>
            <KanbanBoard stories={stories} onStatusChange={handleStatusChange} />
          </div>
        </div>
        
        {/* Sidebar - 1/3 width on large screens */}
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Team</h2>
            <TeamOverview teamMembers={teamMembers} stories={stories} />
          </div>
          
          {/* Project info */}
          <div className="bg-white shadow-sm rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Project Info</h3>
            
            <div className="text-sm text-gray-500 mb-4">
              <p className="mb-2">{activeProject.description}</p>
              {activeProject.vision && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700">Vision</h4>
                  <p className="italic">{activeProject.vision}</p>
                </div>
              )}
            </div>
            
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700">Team Size</h4>
              <p className="text-sm">{teamMembers.length} members</p>
            </div>
            
            <div className="mt-4">
              <a 
                href={`/projects/${activeProject.id}`}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                View Project Details →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;