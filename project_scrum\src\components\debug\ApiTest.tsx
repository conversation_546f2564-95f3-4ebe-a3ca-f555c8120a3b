import React, { useState, useEffect } from 'react';
import { taskAPI } from '../../services/api';

const ApiTest: React.FC = () => {
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);
        const data = await taskAPI.getAllTasks();
        setTasks(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tasks');
        console.error('API Test Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  if (loading) {
    return <div className="p-4 bg-yellow-100 rounded">Loading tasks from API...</div>;
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 rounded">
        <h3 className="font-bold text-red-800">API Error:</h3>
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-green-100 rounded">
      <h3 className="font-bold text-green-800 mb-2">API Connection Successful!</h3>
      <p className="text-green-700 mb-2">Fetched {tasks.length} tasks from backend</p>
      {tasks.length > 0 && (
        <div className="mt-2">
          <h4 className="font-semibold">Sample Task:</h4>
          <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
            {JSON.stringify(tasks[0], null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ApiTest;
