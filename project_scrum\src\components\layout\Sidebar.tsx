import React from 'react';
import {
  LayoutDashboard,
  FolderKanban,
  // ListTodo ,
  Calendar,
  Settings,
  Users,
  ChevronRight,
  ChevronDown,
  Filter
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../context/ProjectContext';
import { useTask } from '../../context/TaskContext';
import { Project } from '../../types';

interface SidebarProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, toggleSidebar }) => {
  const navigate = useNavigate();
  const { projects, currentProject, setCurrentProject } = useProject();
  const { selectedProject, selectedZone, setSelectedProject, setSelectedZone, getTaskStats } = useTask();
  const [expandedProjects, setExpandedProjects] = React.useState(true);
  const [expandedFilters, setExpandedFilters] = React.useState(false);

  // Function to handle project click
  const handleProjectClick = (project: Project) => {
    setCurrentProject(project);
    navigate('/project-dashboard');  // Navigate to the project-specific dashboard
  };

  return (
    <div className={`bg-gray-800 text-white h-screen ${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ease-in-out`}>
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        {!isCollapsed && <h1 className="text-xl font-bold">ScrumMaster</h1>}
        <button
          onClick={toggleSidebar}
          className="p-1 rounded-md hover:bg-gray-700 focus:outline-none"
        >
          <ChevronRight className={`h-5 w-5 transform transition-transform ${isCollapsed ? '' : 'rotate-180'}`} />
        </button>
      </div>

      <nav className="mt-5">
        <ul className="space-y-1">
          <li>
            <a
              href="/dashboard"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <LayoutDashboard className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Dashboard</span>}
            </a>
          </li>

          <li className="mt-4">
            <div
              className={`flex items-center px-4 py-2 text-gray-300 ${isCollapsed ? '' : 'justify-between'} cursor-pointer hover:bg-gray-700 hover:text-white transition-colors`}
              onClick={() => !isCollapsed && setExpandedProjects(!expandedProjects)}
            >
              <div className="flex items-center">
                <FolderKanban className="h-5 w-5" />
                {!isCollapsed && <span className="ml-3">Projects</span>}
              </div>
              {!isCollapsed && (
                <div className="text-gray-400 hover:text-white">
                  {expandedProjects ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              )}
            </div>

            {!isCollapsed && expandedProjects && (
              <ul className="ml-4 mt-1 space-y-1">
                {projects.map(project => (
                  <li key={project.id}>
                    <button
                      onClick={() => handleProjectClick(project)}
                      className={`flex items-center w-full px-4 py-2 text-sm ${
                        currentProject?.id === project.id
                          ? 'bg-gray-700 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      } transition-colors rounded-md`}
                    >
                      {project.name}
                    </button>
                  </li>
                ))}
                <li>
                  <a
                    href="/projects/new"
                    className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded-md"
                  >
                    + New Project
                  </a>
                </li>
              </ul>
            )}
          </li>

          <li>
            {/* <a
              href="/backlog"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <ListTodo className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Backlog</span>}
            </a> */}
          </li>

          <li>
            <a
              href="/sprints"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Calendar className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Sprints</span>}
            </a>
          </li>

          <li>
            <a
              href="/team"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Users className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Team</span>}
            </a>
          </li>

          {/* Active Filters Section */}
          <li className="mt-4">
            <div
              className={`flex items-center px-4 py-2 text-gray-300 ${isCollapsed ? '' : 'justify-between'} cursor-pointer hover:bg-gray-700 hover:text-white transition-colors`}
              onClick={() => !isCollapsed && setExpandedFilters(!expandedFilters)}
            >
              <div className="flex items-center">
                <Filter className="h-5 w-5" />
                {!isCollapsed && <span className="ml-3">Active Filters</span>}
              </div>
              {!isCollapsed && (
                <div className="text-gray-400 hover:text-white">
                  {expandedFilters ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              )}
            </div>

            {!isCollapsed && expandedFilters && (
              <div className="ml-4 mt-1 space-y-2 text-sm">
                {selectedProject && (
                  <div className="px-4 py-2 bg-blue-600 rounded-md flex justify-between items-center">
                    <span>Project: {selectedProject}</span>
                    <button
                      onClick={() => setSelectedProject(null)}
                      className="text-blue-200 hover:text-white"
                    >
                      ×
                    </button>
                  </div>
                )}
                {selectedZone && (
                  <div className="px-4 py-2 bg-green-600 rounded-md flex justify-between items-center">
                    <span>Zone: {selectedZone}</span>
                    <button
                      onClick={() => setSelectedZone(null)}
                      className="text-green-200 hover:text-white"
                    >
                      ×
                    </button>
                  </div>
                )}
                {!selectedProject && !selectedZone && (
                  <div className="px-4 py-2 text-gray-400 text-xs">
                    No active filters
                  </div>
                )}
                <div className="px-4 py-2 text-gray-400 text-xs border-t border-gray-600">
                  Total Tasks: {getTaskStats().total}
                </div>
              </div>
            )}
          </li>

          <li className="mt-auto">
            <a
              href="/settings"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Settings className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Settings</span>}
            </a>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;