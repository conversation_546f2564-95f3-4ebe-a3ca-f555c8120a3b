import React from 'react';
import {
  LayoutDashboard,
  FolderKanban,
  // ListTodo ,
  Calendar,
  Settings,
  Users,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../context/ProjectContext';
import { Project } from '../../types';

interface SidebarProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, toggleSidebar }) => {
  const navigate = useNavigate();
  const { projects, currentProject, setCurrentProject } = useProject();
  const [expandedProjects, setExpandedProjects] = React.useState(true);

  // Function to handle project click
  const handleProjectClick = (project: Project) => {
    setCurrentProject(project);
    navigate('/project-dashboard');  // Navigate to the project-specific dashboard
  };

  return (
    <div className={`bg-gray-800 text-white h-screen ${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 ease-in-out`}>
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        {!isCollapsed && <h1 className="text-xl font-bold">ScrumMaster</h1>}
        <button
          onClick={toggleSidebar}
          className="p-1 rounded-md hover:bg-gray-700 focus:outline-none"
        >
          <ChevronRight className={`h-5 w-5 transform transition-transform ${isCollapsed ? '' : 'rotate-180'}`} />
        </button>
      </div>

      <nav className="mt-5">
        <ul className="space-y-1">
          <li>
            <a
              href="/dashboard"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <LayoutDashboard className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Dashboard</span>}
            </a>
          </li>

          <li className="mt-4">
            <div
              className={`flex items-center px-4 py-2 text-gray-300 ${isCollapsed ? '' : 'justify-between'} cursor-pointer hover:bg-gray-700 hover:text-white transition-colors`}
              onClick={() => !isCollapsed && setExpandedProjects(!expandedProjects)}
            >
              <div className="flex items-center">
                <FolderKanban className="h-5 w-5" />
                {!isCollapsed && <span className="ml-3">Projects</span>}
              </div>
              {!isCollapsed && (
                <div className="text-gray-400 hover:text-white">
                  {expandedProjects ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              )}
            </div>

            {!isCollapsed && expandedProjects && (
              <ul className="ml-4 mt-1 space-y-1">
                {projects.map(project => (
                  <li key={project.id}>
                    <button
                      onClick={() => handleProjectClick(project)}
                      className={`flex items-center w-full px-4 py-2 text-sm ${
                        currentProject?.id === project.id
                          ? 'bg-gray-700 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      } transition-colors rounded-md`}
                    >
                      {project.name}
                    </button>
                  </li>
                ))}
                <li>
                  <a
                    href="/projects/new"
                    className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors rounded-md"
                  >
                    + New Project
                  </a>
                </li>
              </ul>
            )}
          </li>

          <li>
            {/* <a
              href="/backlog"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <ListTodo className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Backlog</span>}
            </a> */}
          </li>

          <li>
            <a
              href="/sprints"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Calendar className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Sprints</span>}
            </a>
          </li>

          <li>
            <a
              href="/team"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Users className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Team</span>}
            </a>
          </li>

          <li className="mt-auto">
            <a
              href="/settings"
              className="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors"
            >
              <Settings className="h-5 w-5" />
              {!isCollapsed && <span className="ml-3">Settings</span>}
            </a>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;