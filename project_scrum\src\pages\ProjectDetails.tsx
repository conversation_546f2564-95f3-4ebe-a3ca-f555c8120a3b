import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProject } from '../context/ProjectContext';
import { useTask } from '../context/TaskContext';

import Button from '../components/ui/Button';
import { Users, Clock, CheckSquare, BarChart3, Target } from 'lucide-react';

function ProjectDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getProjectDetails } = useProject();
  const { getTasksByProject } = useTask();
  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const projectData = await getProjectDetails(id);
        setProject(projectData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch project details');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [id, getProjectDetails]);

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg">Loading project details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Project not found.</p>
      </div>
    );
  }

  const handleViewTasks = () => {
    navigate('/sprints', { state: { projectFilter: project.id } });
  };

  const handleViewBoard = () => {
    navigate('/board', { state: { projectFilter: project.id } });
  };



  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.name}</h1>
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full text-sm">
                <CheckSquare className="w-4 h-4" />
                <span>{project.taskCount} tasks</span>
              </div>
              <div className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full text-sm">
                <Users className="w-4 h-4" />
                <span>{project.team.length} team members</span>
              </div>
              <div className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full text-sm">
                <Clock className="w-4 h-4" />
                <span>{project.totalExpectedHours}h planned</span>
              </div>
              <div className="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full text-sm">
                <BarChart3 className="w-4 h-4" />
                <span>{Math.round(project.progress)}% complete</span>
              </div>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="primary"
              onClick={handleViewBoard}
              leftIcon={<Target className="w-4 h-4" />}
            >
              View Board
            </Button>
            <Button
              variant="secondary"
              onClick={handleViewTasks}
              leftIcon={<CheckSquare className="w-4 h-4" />}
            >
              View Tasks
            </Button>
          </div>
        </div>

        {/* Project Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Progress Overview */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Progress Overview</h3>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Overall Progress</span>
                  <span>{Math.round(project.progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(project.progress, 100)}%` }}
                  ></div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                <div>{project.totalConsumedHours}h / {project.totalExpectedHours}h</div>
              </div>
            </div>
          </div>

          {/* Task Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Task Status</h3>
            <div className="space-y-2">
              {Object.entries(project.statusCounts).map(([status, count]) => (
                <div key={status} className="flex justify-between text-sm">
                  <span className="text-gray-600">{status}</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Zones */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Zones</h3>
            <div className="flex flex-wrap gap-2">
              {project.zones.map((zone: string) => (
                <span key={zone} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {zone}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Team Members */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Team Members</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {project.team.map((member: string) => (
              <div key={member} className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-sm font-medium text-gray-800">{member}</div>
              </div>
            ))}
          </div>
        </div>

      </div>
    </div>
  );
}

export default ProjectDetails;