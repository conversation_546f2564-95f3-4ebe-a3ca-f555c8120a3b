import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProject } from '../context/ProjectContext';
import { users } from '../data/mockData';
import Badge from '../components/ui/Badge';
import Button from '../components/ui/Button';
import { Calendar, Users, Clock, CheckSquare, AlertCircle } from 'lucide-react';

function ProjectDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { projects, updateProject, deleteProject } = useProject();
  const project = projects.find(p => p.id === id);
  
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: project?.name || '',
    description: project?.description || '',
    vision: project?.vision || '',
    team: project?.team || []
  });
  const [error, setError] = useState('');

  if (!project) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Project not found.</p>
      </div>
    );
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.name.trim()) {
      setError('Project name is required');
      return;
    }

    if (!formData.description.trim()) {
      setError('Project description is required');
      return;
    }

    if (formData.team.length === 0) {
      setError('Please select at least one team member');
      return;
    }

    try {
      updateProject({
        ...project,
        ...formData,
        updatedAt: new Date()
      });
      setIsEditing(false);
    } catch (err) {
      setError('Failed to update project');
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      deleteProject(project.id);
      navigate('/projects');
    }
  };

  const teamMembers = users.filter(user => project.team.includes(user.id));

  if (isEditing) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Edit Project</h1>

          <form onSubmit={handleSubmit}>
            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span>{error}</span>
              </div>
            )}

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Project Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Vision
                </label>
                <textarea
                  rows={2}
                  value={formData.vision}
                  onChange={(e) => setFormData({ ...formData, vision: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Team Members <span className="text-red-500">*</span>
                </label>
                <div className="space-y-2 max-h-60 overflow-y-auto p-4 border border-gray-200 rounded-md">
                  {users.map(user => (
                    <label key={user.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.team.includes(user.id)}
                        onChange={(e) => {
                          const newTeam = e.target.checked
                            ? [...formData.team, user.id]
                            : formData.team.filter(id => id !== user.id);
                          setFormData({ ...formData, team: newTeam });
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-900">{user.name}</span>
                      <span className="ml-2 text-xs text-gray-500 capitalize">({user.role})</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Button
                variant="ghost"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.name}</h1>
            <div className="flex items-center gap-4">
              <Badge variant="default" className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>Created: {new Date(project.createdAt).toLocaleDateString()}</span>
              </Badge>
              <Badge variant="default" className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>{teamMembers.length} members</span>
              </Badge>
              <Badge variant="default" className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>Last updated: {new Date(project.updatedAt).toLocaleDateString()}</span>
              </Badge>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="primary"
              onClick={() => setIsEditing(true)}
              leftIcon={<CheckSquare className="w-4 h-4" />}
            >
              Edit Project
            </Button>
            <Button
              variant="danger"
              onClick={handleDelete}
            >
              Delete Project
            </Button>
          </div>
        </div>

        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold text-gray-800 mb-3">Description</h2>
          <p className="text-gray-600">{project.description}</p>

          {project.vision && (
            <>
              <h2 className="text-xl font-semibold text-gray-800 mb-3 mt-6">Vision</h2>
              <p className="text-gray-600">{project.vision}</p>
            </>
          )}
        </div>

        <div className="mt-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Team Members</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {teamMembers.map(member => (
              <div key={member.id} className="flex items-center p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    {member.name.charAt(0)}
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">{member.name}</p>
                  <p className="text-xs text-gray-500 capitalize">{member.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProjectDetails;