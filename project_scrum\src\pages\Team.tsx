// import React from 'react';
// import { useProject } from '../context/ProjectContext';
// import { users } from '../data/mockData';
// import Avatar from '../components/ui/Avatar';

// const Team = () => {
//   const { currentProject } = useProject();
//   const teamMembers = currentProject 
//     ? users.filter(user => currentProject.team.includes(user.id))
//     : [];

//   if (!currentProject) {
//     return (
//       <div className="p-6 text-center">
//         <p className="text-gray-600">Please select a project to view its team.</p>
//       </div>
//     );
//   }

//   return (
//     <div className="p-6">
//       <h1 className="text-2xl font-bold text-gray-900 mb-6">Team Members</h1>

//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//         {teamMembers.map(member => (
//           <div key={member.id} className="bg-white rounded-lg shadow-sm p-6">
//             <div className="flex items-center space-x-4">
//               <Avatar src={member.avatar} name={member.name} size="lg" />
//               <div>
//                 <h3 className="text-lg font-semibold text-gray-900">{member.name}</h3>
//                 <p className="text-sm text-gray-500 capitalize">{member.role}</p>
//                 <p className="text-sm text-gray-500">{member.email}</p>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {teamMembers.length === 0 && (
//         <div className="text-center py-12 bg-white rounded-lg">
//           <p className="text-gray-600">No team members assigned to this project.</p>
//         </div>
//       )}
//     </div>
//   );
// };

// export default Team;


// import React, { useState } from 'react';

// type Member = {
//   name: string;
//   team: string;
//   birthday: string;
//   extension: string;
//   remarks?: string;
// };

// const members: Member[] = [
//   { name: 'Mr. Deepak Gupta', team: 'SZ', birthday: '24-Jan', extension: '4137' },
//   { name: 'Mr. Mayank Sarogi', team: 'SZ', birthday: '07-Aug', extension: '4234' },
//   { name: 'Mr. Ananad Mehra', team: 'L1', birthday: '14-May', extension: '4194' },
//   { name: 'Mr. Pradeep Navhal', team: 'L1', birthday: '20-Nov', extension: '4221' },
//   { name: 'Mr. Mukesh Chauhan', team: 'HW', birthday: '08-Mar', extension: '4154' },
//   { name: 'Mr. Bhuvnesh Kumar', team: 'L1', birthday: '27-Jul', extension: '4180' },
//   { name: 'Mr. Anshul Singhal', team: 'L1', birthday: '21-Jun', extension: '4247' },
//   { name: 'Ms. Jayati Vaid', team: 'Development', birthday: '24-Aug', extension: '4250' },
//   { name: 'Mr. Rajiv Singh', team: 'HW', birthday: '04-Aug', extension: '4246' },
//   { name: 'Mr. Harlal Kumar', team: 'L1', birthday: '07-Feb', extension: '4182' },
//   { name: 'Ms. Himanshi', team: 'IZ', birthday: '07-Sep', extension: '4196' },
//   { name: 'Mr. Kanishk', team: 'L1', birthday: '29-Oct', extension: '4243' },
//   { name: 'Mr. Sunil', team: 'L1', birthday: '02-Feb', extension: '4193' },
//   { name: 'Mr. Soham', team: 'SZ', birthday: '28-Sep', extension: '4176' },
//   { name: 'Mr. Krishan Kumar', team: 'HW', birthday: '15-May', extension: '4183', remarks: 'Will be updated' },
//   { name: 'Mr. Sampad', team: 'SZ', birthday: '11-Sep', extension: '' },
//   { name: 'Ms. Kusum Negi', team: 'IZ', birthday: '03-Sep', extension: '4222' },
//   { name: 'Mr. Gagandeep Singh', team: 'IZ', birthday: '30-Dec', extension: '4242' },
//   { name: 'Mr. Satya Prakash', team: 'IZ', birthday: '06-May', extension: '', remarks: 'Will be updated' },
//   { name: 'Mr. Binoy', team: 'SZ', birthday: '26-Jul', extension: '4238' },
// ];

// // Get unique team names
// const uniqueTeams = Array.from(new Set(members.map((m) => m.team))).sort();

// const Team: React.FC = () => {
//   const [selectedTeam, setSelectedTeam] = useState<string>(uniqueTeams[0]);

//   const filteredMembers = members.filter((member) => member.team === selectedTeam);

//   return (
//     <div className="p-6 min-h-screen bg-gray-100">
//       <h2 className="text-2xl font-bold mb-4">EIC & Automation Team</h2>

//       <div className="mb-4">
//         <label htmlFor="teamSelect" className="mr-2 font-medium">
//           Select Team:
//         </label>
//         <select
//           id="teamSelect"
//           value={selectedTeam}
//           onChange={(e) => setSelectedTeam(e.target.value)}
//           className="p-2 border rounded-md"
//         >
//           {uniqueTeams.map((team) => (
//             <option key={team} value={team}>
//               {team}
//             </option>
//           ))}
//         </select>
//       </div>

//       <div className="bg-white shadow rounded-md p-4">
//         <h3 className="text-lg font-semibold mb-3">Members of {selectedTeam}</h3>
//         <table className="w-full text-left border">
//           <thead>
//             <tr className="bg-gray-200">
//               <th className="p-2 border">Name</th>
//               <th className="p-2 border">Birthday</th>
//               <th className="p-2 border">Extension</th>
//               <th className="p-2 border">Remarks</th>
//             </tr>
//           </thead>
//           <tbody>
//             {filteredMembers.map((member, index) => (
//               <tr key={index} className="border-t">
//                 <td className="p-2 border">{member.name}</td>
//                 <td className="p-2 border">{member.birthday}</td>
//                 <td className="p-2 border">{member.extension || '—'}</td>
//                 <td className="p-2 border">{member.remarks || '—'}</td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// };

// export default Team;





// import React from 'react';

// //pe Role = 'Admin' | 'Scrum_master' | 'Product_owner'  | 'Developer';

// interface TeamMember {
//   name: string;
//   role: string;
//   team: string;
//   avatarUrl: string;
// }

// const teamMembers: TeamMember[] = [
//   {
//     name: 'Deepak Gupta',
//     role: 'L1 Automation',
//     team: 'L1',
//     avatarUrl: '/profile/anand.jpg',
//   },
//   {
//     name: 'Anand',
//     role: 'L1 Automation',
//     team: 'L1',
//     avatarUrl: '/profile/anand.jpg',
//   },
//   {
//     name: 'Kusum',
//     role: 'IZ',
//     team: 'IZ',
//     avatarUrl: '/profile/kusum.jpg',
//   },
//   {
//     name: 'Kishan',
//     role: 'Hardware',
//     team: 'HW',
//     avatarUrl: '/profile/kishan.jpg',
//   },
//   {
//     name: 'Rajiv',
//     role: 'Hardware',
//     team: 'HW',
//     avatarUrl: '/profile/rajiv.jpg',
//   },
//   {
//     name: 'Himanshi',
//     role: 'IZ',
//     team: 'IZ',
//     avatarUrl: '/profile/himanshi.jpg',
//   },
// ];

// const Team: React.FC = () => {
//   return (
//     <div className="p-6 bg-gray-50 min-h-screen">
//       <h2 className="text-2xl font-semibold mb-4">Team</h2>

//       <div className="bg-white rounded-lg shadow-md p-4">
//         <h3 className="text-lg font-semibold mb-4">Team Overview</h3>

//         {teamMembers.map((member, index) => (
//           <div
//             key={index}
//             className="flex items-center justify-between py-4 border-b last:border-b-0"
//           >
//             <div className="flex items-center space-x-4">
//               <img
//                 src={member.avatarUrl}
//                 alt={member.name}
//                 className="w-12 h-12 rounded-full object-cover"
//               />
//               <div>
//                 <h4 className="font-medium">{member.name}</h4>
//                 <p className="text-sm text-gray-500">Team: {member.team}</p>
//               </div>
//             </div>

//             <span className="bg-gray-100 text-sm px-3 py-1 rounded-full text-gray-700 capitalize">
//               {member.role.replace('_', ' ')}
//             </span>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Team;




// import React, { useState } from 'react';

// type Member = {
//   name: string;
//   team: string;
//   birthday: string;
//   extension: string;
// };

// const initialMembers: Member[] = [
//   { name: 'Mr. Deepak Gupta', team: 'SZ', birthday: '24-Jan', extension: '4137' },
//   { name: 'Mr. Mayank Sarogi', team: 'SZ', birthday: '07-Aug', extension: '4234' },
//   { name: 'Mr. Ananad Mehra', team: 'L1', birthday: '14-May', extension: '4194' },
//   { name: 'Mr. Pradeep Navhal', team: 'L1', birthday: '20-Nov', extension: '4221' },
//   { name: 'Mr. Mukesh Chauhan', team: 'HW', birthday: '08-Mar', extension: '4154' },
//   { name: 'Mr. Bhuvnesh Kumar', team: 'L1', birthday: '27-Jul', extension: '4180' },
//   { name: 'Mr. Anshul Singhal', team: 'L1', birthday: '21-Jun', extension: '4247' },
//   { name: 'Ms. Jayati Vaid', team: 'Development', birthday: '24-Aug', extension: '4250' },
//   { name: 'Mr. Rajiv Singh', team: 'HW', birthday: '04-Aug', extension: '4246' },
//   { name: 'Mr. Harlal Kumar', team: 'L1', birthday: '07-Feb', extension: '4182' },
//   { name: 'Ms. Himanshi', team: 'IZ', birthday: '07-Sep', extension: '4196' },
//   { name: 'Mr. Kanishk', team: 'L1', birthday: '29-Oct', extension: '4243' },
//   { name: 'Mr. Sunil', team: 'L1', birthday: '02-Feb', extension: '4193' },
//   { name: 'Mr. Soham', team: 'SZ', birthday: '28-Sep', extension: '4176' },
//   { name: 'Mr. Krishan Kumar', team: 'HW', birthday: '15-May', extension: '4183' },
//   { name: 'Mr. Sampad', team: 'SZ', birthday: '11-Sep', extension: '' },
//   { name: 'Ms. Kusum Negi', team: 'IZ', birthday: '03-Sep', extension: '4222' },
//   { name: 'Mr. Gagandeep Singh', team: 'IZ', birthday: '30-Dec', extension: '4242' },
//   { name: 'Mr. Satya Prakash', team: 'IZ', birthday: '06-May', extension: '' },
//   { name: 'Mr. Binoy', team: 'SZ', birthday: '26-Jul', extension: '4238' },
// ];

// const memberImages: Record<string, string> = {
//   'Mr. Deepak Gupta': '/blank.jpg',
//   'Mr. Mayank Sarogi': '/blank.jpg',
//   'Mr. Ananad Mehra': '/blank.jpg',
//   'Mr. Pradeep Navhal': '/blank.jpg',
//   'Mr. Mukesh Chauhan': '/blank.jpg',
//   'Mr. Bhuvnesh Kumar': '/blank.jpg',
//   'Mr. Anshul Singhal': '/anshul_dci.jpg',
//   'Ms. Jayati Vaid': '/blank.jpg',
//   'Mr. Rajiv Singh': '/rajiv_dci.jpg',
//   'Mr. Harlal Kumar': '/blank.jpg',
//   'Ms. Himanshi': '/blank.jpg',
//   'Mr. Kanishk': '/blank.jpg',
//   'Mr. Sunil': '/blank.jpg',
//   'Mr. Soham': '/soham_dci.jpg',
//   'Mr. Krishan Kumar': '/blank.jpg',
//   'Mr. Sampad': '/sampad_dci.jpg',
//   'Ms. Kusum Negi': '/kusum_dci.jpg',
//   'Mr. Gagandeep Singh': '/blank.jpg',
//   'Mr. Satya Prakash': '/blank.jpg',
//   'Mr. Binoy': '/binoy_dci.jpg',
// };

// const Team: React.FC = () => {
//   const [members, setMembers] = useState<Member[]>(initialMembers);
//   const teams = ['All', ...Array.from(new Set(members.map((m) => m.team))).sort()];
//   const [selectedTeam, setSelectedTeam] = useState('All');
//   const [showForm, setShowForm] = useState(false);

//   const [formData, setFormData] = useState<Member>({
//     name: '',
//     team: '',
//     birthday: '',
//     extension: '',
//   });

//   const filteredMembers =
//     selectedTeam === 'All' ? members : members.filter((m) => m.team === selectedTeam);

//   const handleAddMember = (e: React.FormEvent) => {
//     e.preventDefault();
//     setMembers([...members, formData]);
//     setFormData({ name: '', team: selectedTeam === 'All' ? '' : selectedTeam, birthday: '', extension: '' });
//     setShowForm(false);
//   };

//   return (
//     <div className="p-6 min-h-screen bg-gray-100">
//       <div className="flex justify-between items-center mb-4">
//         <h2 className="text-2xl font-bold">EIC & Automation Team</h2>
//         <button
//           onClick={() => {
//             setShowForm(true);
//             setFormData({ name: '', team: selectedTeam === 'All' ? '' : selectedTeam, birthday: '', extension: '' });
//           }}
//           className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
//         >
//           + Add Member
//         </button>
//       </div>

//       <div className="mb-6">
//         <label className="mr-2 font-medium">Select Team:</label>
//         <select
//           value={selectedTeam}
//           onChange={(e) => setSelectedTeam(e.target.value)}
//           className="p-2 border rounded"
//         >
//           {teams.map((team) => (
//             <option key={team} value={team}>
//               {team}
//             </option>
//           ))}
//         </select>
//       </div>

//       {showForm && (
//         <form onSubmit={handleAddMember} className="bg-white p-4 rounded shadow mb-6">
//           <h3 className="text-lg font-semibold mb-4">Add New Member</h3>
//           <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//             <input
//               type="text"
//               placeholder="Name"
//               value={formData.name}
//               required
//               onChange={(e) => setFormData({ ...formData, name: e.target.value })}
//               className="border p-2 rounded"
//             />
//             <input
//               type="text"
//               placeholder="Birthday (e.g. 01-Jan)"
//               value={formData.birthday}
//               required
//               onChange={(e) => setFormData({ ...formData, birthday: e.target.value })}
//               className="border p-2 rounded"
//             />
//             <input
//               type="text"
//               placeholder="Extension"
//               value={formData.extension}
//               onChange={(e) => setFormData({ ...formData, extension: e.target.value })}
//               className="border p-2 rounded"
//             />
//             <select
//               value={formData.team}
//               required
//               onChange={(e) => setFormData({ ...formData, team: e.target.value })}
//               className="border p-2 rounded"
//             >
//               <option value="" disabled>Select Team</option>
//               {teams.filter((t) => t !== 'All').map((team) => (
//                 <option key={team} value={team}>
//                   {team}
//                 </option>
//               ))}
//             </select>
//           </div>
//           <div className="mt-4 text-right">
//             <button type="submit" className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
//               Save Member
//             </button>
//           </div>
//         </form>
//       )}

//       <h3 className="text-xl font-semibold mb-4">
//         Members of {selectedTeam === 'All' ? 'All Teams' : selectedTeam}
//       </h3>

//       <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
//         {filteredMembers.map((member) => (
//           <div
//             key={member.name}
//             className="bg-white p-4 rounded shadow text-center flex flex-col items-center"
//           >
//             <img
//               src={memberImages[member.name] || '/blank.jpg'}
//               alt={member.name}
//               className="w-24 h-24 rounded-full object-cover mb-3"
//             />
//             <h4 className="font-semibold">{member.name}</h4>
//             <p className="text-sm text-gray-600">Birthday: {member.birthday}</p>
//             <p className="text-sm text-gray-600">Extension: {member.extension}</p>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Team;


import React, { useState } from 'react';

type Member = {
  name: string;
  team: string;
  birthday: string;
  extension: string;
};

const initialMembers: Member[] = [
  { name: 'Mr. Deepak Gupta', team: 'SZ', birthday: '24-Jan', extension: '4137' },
  { name: 'Mr. Mayank Sarogi', team: 'SZ', birthday: '07-Aug', extension: '4234' },
  { name: 'Mr. Ananad Mehra', team: 'L1', birthday: '14-May', extension: '4194' },
  { name: 'Mr. Pradeep Navhal', team: 'L1', birthday: '20-Nov', extension: '4221' },
  { name: 'Mr. Mukesh Chauhan', team: 'HW', birthday: '08-Mar', extension: '4154' },
  { name: 'Mr. Bhuvnesh Kumar', team: 'L1', birthday: '27-Jul', extension: '4180' },
  { name: 'Mr. Anshul Singhal', team: 'L1', birthday: '21-Jun', extension: '4247' },
  { name: 'Ms. Jayati Vaid', team: 'Development', birthday: '24-Aug', extension: '4250' },
  { name: 'Mr. Rajiv Singh', team: 'HW', birthday: '04-Aug', extension: '4246' },
  { name: 'Mr. Harlal Kumar', team: 'L1', birthday: '07-Feb', extension: '4182' },
  { name: 'Ms. Himanshi', team: 'IZ', birthday: '07-Sep', extension: '4196' },
  { name: 'Mr. Kanishk', team: 'L1', birthday: '29-Oct', extension: '4243' },
  { name: 'Mr. Sunil', team: 'L1', birthday: '02-Feb', extension: '4193' },
  { name: 'Mr. Soham', team: 'SZ', birthday: '28-Sep', extension: '4176' },
  { name: 'Mr. Krishan Kumar', team: 'HW', birthday: '15-May', extension: '4183' },
  { name: 'Mr. Sampad', team: 'SZ', birthday: '11-Sep', extension: '' },
  { name: 'Ms. Kusum Negi', team: 'IZ', birthday: '03-Sep', extension: '4222' },
  { name: 'Mr. Gagandeep Singh', team: 'IZ', birthday: '30-Dec', extension: '4242' },
  { name: 'Mr. Satya Prakash', team: 'IZ', birthday: '06-May', extension: '' },
  { name: 'Mr. Binoy', team: 'SZ', birthday: '26-Jul', extension: '4238' },
];

const memberImages: Record<string, string> = {
  'Mr. Deepak Gupta': '/blank.jpg',
  'Mr. Mayank Sarogi': '/blank.jpg',
  'Mr. Ananad Mehra': '/blank.jpg',
  'Mr. Pradeep Navhal': '/blank.jpg',
  'Mr. Mukesh Chauhan': '/blank.jpg',
  'Mr. Bhuvnesh Kumar': '/blank.jpg',
  'Mr. Anshul Singhal': '/anshul_dci.jpg',
  'Ms. Jayati Vaid': '/blank.jpg',
  'Mr. Rajiv Singh': '/rajiv_dci.jpg',
  'Mr. Harlal Kumar': '/blank.jpg',
  'Ms. Himanshi': '/blank.jpg',
  'Mr. Kanishk': '/blank.jpg',
  'Mr. Sunil': '/blank.jpg',
  'Mr. Soham': '/soham_dci.jpg',
  'Mr. Krishan Kumar': '/blank.jpg',
  'Mr. Sampad': '/sampad_dci.jpg',
  'Ms. Kusum Negi': '/kusum_dci.jpg',
  'Mr. Gagandeep Singh': '/blank.jpg',
  'Mr. Satya Prakash': '/blank.jpg',
  'Mr. Binoy': '/binoy_dci.jpg',
};

const Team: React.FC = () => {
  const [members, setMembers] = useState<Member[]>(initialMembers);
  const teams = ['All', ...Array.from(new Set(members.map((m) => m.team))).sort()];
  const [selectedTeam, setSelectedTeam] = useState('All');
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<Member>({
    name: '',
    team: '',
    birthday: '',
    extension: '',
  });
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const filteredMembers =
    selectedTeam === 'All' ? members : members.filter((m) => m.team === selectedTeam);

  const handleAddOrUpdateMember = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingIndex !== null) {
      const updatedMembers = [...members];
      updatedMembers[editingIndex] = formData;
      setMembers(updatedMembers);
      setEditingIndex(null);
    } else {
      setMembers([...members, formData]);
    }
    setFormData({ name: '', team: selectedTeam === 'All' ? '' : selectedTeam, birthday: '', extension: '' });
    setShowForm(false);
  };

  const handleEditClick = (index: number) => {
    setEditingIndex(index);
    setFormData(members[index]);
    setShowForm(true);
  };

  return (
    <div className="p-6 min-h-screen bg-gray-100">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">EIC & Automation Team</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setFormData({ name: '', team: selectedTeam === 'All' ? '' : selectedTeam, birthday: '', extension: '' });
            setEditingIndex(null);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          + Add Member
        </button>
      </div>

      <div className="mb-6">
        <label className="mr-2 font-medium">Select Team:</label>
        <select
          value={selectedTeam}
          onChange={(e) => setSelectedTeam(e.target.value)}
          className="p-2 border rounded"
        >
          {teams.map((team) => (
            <option key={team} value={team}>
              {team}
            </option>
          ))}
        </select>
      </div>

      {showForm && (
        <form onSubmit={handleAddOrUpdateMember} className="bg-white p-4 rounded shadow mb-6 relative">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">{editingIndex !== null ? 'Edit Member' : 'Add New Member'}</h3>
            <button
              type="button"
              onClick={() => {
                setShowForm(false);
                setEditingIndex(null);
              }}
              className="text-gray-600 hover:text-red-600 text-2xl font-bold"
              aria-label="Close"
            >
              &times;
            </button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <input
              type="text"
              placeholder="Name"
              value={formData.name}
              required
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="border p-2 rounded"
            />
            <input
              type="text"
              placeholder="Birthday (e.g. 01-Jan)"
              value={formData.birthday}
              required
              onChange={(e) => setFormData({ ...formData, birthday: e.target.value })}
              className="border p-2 rounded"
            />
            <input
              type="text"
              placeholder="Extension"
              value={formData.extension}
              onChange={(e) => setFormData({ ...formData, extension: e.target.value })}
              className="border p-2 rounded"
            />
            <select
              value={formData.team}
              required
              onChange={(e) => setFormData({ ...formData, team: e.target.value })}
              className="border p-2 rounded"
            >
              <option value="" disabled>Select Team</option>
              {teams.filter((t) => t !== 'All').map((team) => (
                <option key={team} value={team}>
                  {team}
                </option>
              ))}
            </select>
          </div>
          <div className="mt-4 text-right">
            <button type="submit" className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
              {editingIndex !== null ? 'Update Member' : 'Save Member'}
            </button>
          </div>
        </form>
      )}

      <h3 className="text-xl font-semibold mb-4">
        Members of {selectedTeam === 'All' ? 'All Teams' : selectedTeam}
      </h3>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {filteredMembers.map((member, index) => {
          const realIndex = members.findIndex((m) => m.name === member.name && m.extension === member.extension);
          return (
            <div
              key={member.name + member.extension}
              className="bg-white p-4 rounded shadow text-center flex flex-col items-center"
            >
              <img
                src={memberImages[member.name] || '/blank.jpg'}
                alt={member.name}
                className="w-24 h-24 rounded-full object-cover mb-3"
              />
              <h4 className="font-semibold">{member.name}</h4>
              <p className="text-sm text-gray-600">Birthday: {member.birthday}</p>
              <p className="text-sm text-gray-600">Extension: {member.extension}</p>
              <p className="text-sm text-gray-600">Team: {member.team}</p>
              <button
                onClick={() => handleEditClick(realIndex)}
                className="p-1 bg-blue-600 rounded px-2 py-1 mt-2 text-white hover:underline hover:bg-blue-700 text-sm"
              >
                Edit
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Team;
