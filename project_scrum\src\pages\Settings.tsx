import React from 'react';
import { useAuth } from '../context/AuthContext';

const Settings = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-600">Please log in to access settings.</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Settings</h1>

      <div className="max-w-2xl space-y-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Notifications</h2>
          <div className="space-y-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                defaultChecked
              />
              <span className="ml-2 text-gray-700">Email notifications</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                defaultChecked
              />
              <span className="ml-2 text-gray-700">Sprint updates</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                defaultChecked
              />
              <span className="ml-2 text-gray-700">Task assignments</span>
            </label>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Theme</h2>
          <div className="space-y-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="theme"
                className="text-blue-600 focus:ring-blue-500"
                defaultChecked
              />
              <span className="ml-2 text-gray-700">Light</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="theme"
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-gray-700">Dark</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="theme"
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-gray-700">System</span>
            </label>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Language</h2>
          <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="en">English</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
            <option value="de">Deutsch</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default Settings;