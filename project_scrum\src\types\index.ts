// User Types
export enum UserRole {
  IZ = 'IZ',
  L1 = 'L1',
  HW = 'HW',
  DEVELOPMENT = 'DEVELOPMENT',
  SZ = 'SZ',
  ADMIN = 'ADMIN'
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
}

// Project Types (derived from backend task data)
export interface Project {
  id: string;
  name: string;
  taskCount: number;
  zones: string[];
  team: string[]; // Array of assignee names
  totalExpectedHours: number;
  totalConsumedHours: number;
  progress: number; // Percentage
  statusCounts: Record<string, number>;
  tasks?: BackendTask[]; // Optional full task data
}

// Legacy Project interface for compatibility
export interface LegacyProject {
  id: string;
  name: string;
  description: string;
  team: string[];
  vision?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Story/Task Types
export enum Priority {
  LOW = '',
  MEDIUM = '',
  HIGH = '',
  CRITICAL = ''
}

export enum Status {
  BACKLOG = 'Backlog',
  WORKING = 'Working',
  CHECK = 'Check',
  DONE = 'Done'
}

export interface Story {
  id: string;
  title: string;
  description: string;
  status: Status;
  assigneeId?: string;
  sprintId?: string;
  projectId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Sprint Types
export interface Sprint {
  id: string;
  name: string;
  projectId: string;
  startDate: Date;
  endDate: Date;
  goals?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Backend Task Types (matching Django model)
export interface BackendTask {
  s_no: number;
  project_zone_no_zone: string;
  active_list: string | null;
  job_description: string | null;
  expected_hours: number | null;
  consumed_hours: number | null;
  zone: string | null;
  responsible_1: string | null;
  responsible_2: string | null;
  job_status: string | null;
}

// Frontend Task Types (for UI components)
export interface Task {
  id: string;
  projectId: string;
  assignedTo: string;
  assignedTo2?: string;
  description: string;
  expectedHours: number;
  consumedHours: number;
  status: string;
  zone: string;
  activeList?: string;
  jobTitle?: string;
  team?: string;
  jobNumber?: number;
}