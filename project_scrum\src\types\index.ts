// User Types
export enum UserRole {
  IZ = 'IZ',
  L1 = 'L1',
  HW = 'HW',
  DEVELOMENT = 'DEVELOPMENT',
  SZ = 'SZ'
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar?: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  team: string[]; // Array of user IDs
  vision?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Story/Task Types
export enum Priority {
  LOW = '',
  MEDIUM = '',
  HIGH = '',
  CRITICAL = ''
}

export enum Status {
  BACKLOG = 'Backlog',
  WORKING = 'Working',
  CHECK = 'Check',
  DONE = 'Done'
}

export interface Story {
  id: string;
  title: string;
  description: string;
  status: Status;
  assigneeId?: string;
  sprintId?: string;
  projectId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Sprint Types
export interface Sprint {
  id: string;
  name: string;
  projectId: string;
  startDate: Date;
  endDate: Date;
  goals?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}